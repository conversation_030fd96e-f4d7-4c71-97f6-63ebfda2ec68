import redis from 'redis';
import { pubClient, subClient } from './redis.adapter';
import { RedisIoStreamAdapter } from './redis-stream.adapter'
import { LogService } from 'src/log/log.service';
export class RedisService {

  redisClient = pubClient;
  constructor() {
    this.redisClient.on('connect', (args) => {
      console.log('redis connection established: ', args);
    });

    this.redisClient.on('error', (err) => {
      console.error('redis connection error:', err);
    });


  }
  setUserSocket(userId: string, socketId: string) {
    this.redisClient.set(`socket:${userId}`, socketId);
  }
  getMemberSocket(userId: string) {
    return this.redisClient.get(`socket:${userId}`)
  }

  async acquireLock(resourceKey: string, lockValue: string, expirySeconds: number): Promise<boolean> {
    if (!this.redisClient.isOpen) { await this.redisClient.connect(); }
    const result = await this.redisClient.set(resourceKey, lockValue, { NX: true, EX: 60 })
    console.log('acquire result: ', result);
    return result === 'OK';
  }

  async releaseLock(resourceKey: string, lockValue: string): Promise<boolean> {
    if (!this.redisClient.isOpen) { await this.redisClient.connect(); }
    const result = await this.redisClient.del(resourceKey)
    console.log('release resource: ', result);
    return result === 1;
  }
}
