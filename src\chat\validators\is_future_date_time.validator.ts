import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';
import { parseISO, addMinutes, isAfter } from 'date-fns';

@ValidatorConstraint({ name: 'IsFutureDateTime', async: false })
export class IsFutureDateTimeConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const dueDate: Date = parseISO(value.dueDate);
    const dueTime: Date = parseISO(value.time);

    // Combine dueDate and dueTime into a single dueDateTime
    const dueDateTime: Date = addMinutes(dueDate, dueTime.getHours() * 60 + dueTime.getMinutes());

    // Compare dueDateTime with currentDateTime
    const currentDateTime = new Date();

    return isAfter(dueDateTime, currentDateTime); // Check if dueDateTime is in the future
  }

  defaultMessage(args: ValidationArguments) {
    return 'Due date and time must be in the future';
  }
}
