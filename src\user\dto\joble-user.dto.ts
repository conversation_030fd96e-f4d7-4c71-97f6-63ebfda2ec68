import { IsString, IsEmail, IsBoolean, IsNotEmpty, IsOptional } from 'class-validator';

export class <PERSON>leUserDto {
    @IsString()
    @IsOptional()
    Id: string;

    @IsString()
    @IsNotEmpty()
    FirstName: string;

    @IsString()
    @IsNotEmpty()
    LastName: string;

    @IsEmail()
    @IsNotEmpty()
    Email: string;

    @IsEmail()
    @IsOptional()
    CompanyEmail: string;

    @IsString()
    @IsNotEmpty()
    Username: string;

    @IsString()
    @IsOptional()
    IpAddress: string;

    @IsBoolean()
    @IsOptional()
    IsIpLock: boolean;

    @IsString()
    @IsNotEmpty()
    RoleName: string;

    @IsString()
    @IsOptional()
    RegionName: string;

    @IsString()
    @IsOptional()
    Address: string;

    @IsString()
    @IsOptional()
    Bio: string;

    @IsString()
    @IsOptional()
    PhoneNumber: string;

    @IsString()
    @IsOptional()
    ProfilePictureUrl: string;

    @IsString()
    @IsOptional()
    Country: string;

    @IsString()
    @IsOptional()
    State: string;

    @IsString()
    @IsOptional()
    ZipCode: string;

    @IsString()
    @IsOptional()
    TimeZone: string;

    @IsBoolean()
    @IsOptional()
    IsVerified: boolean;

    @IsOptional()
    @IsString()
    PhoneNo: string;

    @IsOptional()
    @IsString()
    CompanyId: string;

    @IsOptional()
    @IsString()
    TenantId: string;
}
