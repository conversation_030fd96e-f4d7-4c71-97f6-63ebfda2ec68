import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from './v1/user.service';

@Injectable()
export class AuthService {
    constructor(
        private usersService: UserService,
        private jwtService: JwtService
    ) { }

    async authorize(user_id: string, subdomain?: string) {
        const user = await this.usersService.findByUserId(user_id);
        if (!user) {
            throw new UnauthorizedException();
        }
        const payload = { user_id: user.user_id, username: user.username, id: user.id, };
        return {
            access_token: await this.jwtService.signAsync(payload),
        };
    }
}
