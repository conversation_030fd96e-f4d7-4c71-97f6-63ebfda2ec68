import { ConnectionDto } from 'src/messages/dto/connection.dto';
import { JoinedRoomDto } from 'src/messages/dto/joined-room.dto';
import { MessageDto } from 'src/messages/dto/message.dto';
import { RoomDto } from 'src/messages/dto/room.dto';

export class UserDto {
    username: string;
    email: string; //to be removed
    phone: string; //to be removed
    user_id: string;
    tenantId: string;
    companyName: string; // to be removed
    rooms?: RoomDto[];
    connections?: UserDto[];
    messages?: MessageDto[];//to be removed
    profilePictureUrl: string;//to be removed
    colorPreference: string;
    id: string;
    _id?: string;
    subdomain: string;
}
