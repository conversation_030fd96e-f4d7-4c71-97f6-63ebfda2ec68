/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { ChatGateway } from './chat.gateway';
import { ChatMessageService } from './chat-message.service';
import { ChatMessageHttpDto, ChatUserDto } from 'src/dto/dtos';
import { LogService } from 'src/log/log.service';

@Injectable()
export class HttpmessageService {
    constructor(private chatGateway: ChatGateway, private logService: LogService, private chatMessageService: ChatMessageService) {

    }
    async handleHttpChatMessage(data: ChatMessageHttpDto, sender: ChatUserDto, tenantId: string) {
        const saved = await this.chatMessageService.findChatAndCreateMessage(data, sender, tenantId);
        const res = this.chatGateway.broadcastHttpMessage(saved);
        this.logService.log('http message res: ', res);
        return saved;
    }
}
