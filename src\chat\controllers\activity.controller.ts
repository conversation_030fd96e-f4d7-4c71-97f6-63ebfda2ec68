/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Get, Param, ParseIntPipe, Patch, Query } from '@nestjs/common';
import { ActivityService } from '../services/activity.service';
import { ACTIVITY_TYPE, ChatUserDto } from 'src/dto/dtos';
import { GetUser } from 'src/get-user.decorator';
import { ApiTags } from '@nestjs/swagger';
import { GetTenantId } from 'src/get-tenantId.decorator';
import { GetJobProUserId } from 'src/get-joprouserid.decorator';
import { LogService } from 'src/log/log.service';

@ApiTags('Activity V2')
@Controller('v2/activity')
export class ActivityController {
  constructor(private activityService: ActivityService, private logService: LogService) { }
  @Get('/:id')
  getActivityById(@Param('id') id: string) {
    return this.activityService.findById(id);
  }
  @Get()
  async find(
    @Query('page', ParseIntPipe) page: number,
    @Query('pageSize', ParseIntPipe) pageSize: number,
    @Query('activityType') activityType: ACTIVITY_TYPE,
    @GetUser() user: ChatUserDto,
    @GetTenantId() tenantId: string,
    @GetJobProUserId() jobProUserId: string
  ) {
    this.logService.log('page: ', page, 'pageSize: ', pageSize, 'activityType: ', activityType);
    return this.activityService.find(page, pageSize, activityType, user, tenantId);
  }
  @Patch('/:id')
  updateActivityReadStatus(@Param('id') id: string) {
    return this.activityService.updateReadStatus(id);
  }
}
