import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { IsFutureDateTimeConstraint } from "src/chat/validators/is_future_date_time.validator";
import { FileDto } from "src/messages/dto/file.dto";

@Schema({
    timestamps: true,
    virtuals: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class ScheduledMessage {
    @Prop() userId: string;
    @Prop() jobProUserId: string;
    @Prop() text: string;
    @Prop() files: FileDto[];
    @Prop() chatId: string;
    @Prop() isRecurring: boolean;
    @Prop() days: string[];
    @Prop() time: string;
    @Prop({
        type: Date,
        validate: {
            validator: function (value: Date) {
                const scheduledDateTime = new Date(this.dueDate.toISOString().slice(0, 10) + 'T' + this.time);
                return scheduledDateTime >= new Date();
            },
            message: "Due date and time cannot be in the past",
        },

    }) dueDate: Date;
    @Prop() createdAt: Date;
    @Prop() updatedAt: Date;
}

export const ScheduledMessageSchema = SchemaFactory.createForClass(ScheduledMessage);