/*
https://docs.nestjs.com/controllers#controllers
*/

import { MailerService } from '@nestjs-modules/mailer';
import { Body, Controller, Post } from '@nestjs/common';
import { MailgunService } from '../services/mailgun.service';

@Controller('v1/send-email')
export class SendMailController { 
    constructor(private mailService:MailgunService){}
    @Post()
 sendEmail(@Body() body:any){
    return this.mailService.sendMail({to:'<EMAIL>',from:'<EMAIL>',sender:'<PERSON>',subject:'Just a test email',text:'This is the content of the test email'})
 }   
}
