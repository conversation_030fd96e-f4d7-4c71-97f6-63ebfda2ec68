//* eslint-disable prettier/prettier */
import { UserDto } from 'src/user/dto/user.dto';
import { FileDto } from './file.dto';
import { MESSAGE_TYPE } from './message_type.enum';
import { ReactionDto } from './reaction.dto';
import { CHAT_CALL_FORMAT } from './chat-call.dto';

export class MessageDto {
    text: string;
    is_private: boolean;

    user: UserDto | string;

    room?: string;
    messageType?: MESSAGE_TYPE
    receiverId?: UserDto | string;
    files?: FileDto[]
    mentions?: string[];
    subdomain?: string;
    reactions?: ReactionDto[]
    parentId?: string;

    id?: string;
    isDeleted?: boolean;
    readBy?: string[];

    isPinned?: boolean;
    messageId?: string;
    pinnedBy?: string;
    callType?: CHAT_CALL_FORMAT;
    // call specifics


}
