
import { IsString, <PERSON><PERSON><PERSON>, IsDate, IsObject, ValidateNested, <PERSON><PERSON><PERSON>ber, IsOptional, IsEmail, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export const LIVE_EVENT_NAME = 'LIVE_CHAT_EVENT';

export enum LIVE_CHAT_EVENTS {
    START_CHAT = 'START_CHAT',
    END_CHAT = 'END_CHAT',
    NEW_MESSAGE = 'NEW_MESSAGE',
    JOIN_CHAT = 'JOIN_CHAT',
    UPGRADE_TO_TICKET = 'UPGRADE_TO_TICKET',
    ADD_CHAT_REVIEW = 'ADD_CHAT_REVIEW',
    GET_CHAT_EXISTING_MESSAGES = "GET_CHAT_EXISTING_MESSAGES",
    GET_OPEN_CHATS = "GET_OPEN_CHATS",
    REJOIN_CHAT = "REJOIN_CHAT",
    NEW_CHAT = "NEW_CHAT",
    GET_ACTIVE_CHATS = "GET_ACTIVE_CHATS",
    LEAVE_CHAT = "LEAVE_CHAT"
}
export class LiveChaatPayloadDto {
    event: LIVE_CHAT_EVENTS;
    data: any;
    error?: any
}

export enum ChatStatus {
    open = 'open',
    closed = 'closed',
    active = "active",
}

export enum MessageType {
    text,
    file,
}
export class UpdateProfilePictureDto {
    @IsOptional()
    @IsString() userId?: string;
    @IsString() profilePicture: string
}
export class LiveChatFile {
    @IsString()
    fileName: string;
    @IsNumber()
    fileSize: number;
    @IsString()
    fileUrl: string;
}
export class LiveChatMessage {
    @IsString()
    messageType: MessageType;

    @IsOptional()
    @IsString()
    content?: string;


    @IsDate()
    createdAt: Date; @IsDate()
    updatedAt: Date;

    @IsOptional()
    @IsString()
    senderId: string;

    @IsObject()
    @ValidateNested()
    files?: LiveChatFile[];
    @IsString() chatId: string;
    @IsBoolean()
    @IsOptional() isSystemGenerated?: boolean;
    @IsOptional()
    @IsString() id: string;
}

export class LiveChatDto {
    @IsString()
    @IsOptional()
    id?: string;

    @IsString()
    clientId: string;

    @IsOptional()
    @IsString()
    supportRepId?: string;

    @IsEnum(ChatStatus)
    chatStatus: ChatStatus;

    @IsOptional()
    @IsDate()
    createdAt?: Date;

    @Type(() => LiveChatMessage)
    @ValidateNested()
    messages?: LiveChatMessage[];
}

export class LiveChatUserDto {
    @IsString()
    @IsOptional() jobId: string;
    @IsEmail() email: string;
    @IsString()
    @IsOptional() phone?: string;
    @IsString()
    @IsOptional() firstName?: string;

    @IsString()
    @IsOptional() lastName?: string;


    @IsOptional()
    @IsString() profilePicture?: string;
    @IsString()
    @IsOptional() tenantId?: string;

    @IsString()
    @IsOptional() role?: string;
    @IsDate()
    @IsOptional() createdAt?: Date;
    @IsDate()
    @IsOptional() updatedAt?: Date;
}
