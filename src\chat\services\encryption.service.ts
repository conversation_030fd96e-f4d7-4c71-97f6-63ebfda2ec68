/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import crypto from 'node:crypto';

@Injectable()
export class EncryptionService {
    generateSymmetricKey(): string {
        return crypto.randomBytes(32).toString('hex'); 
      }
      
      encryptSymmetricKey(symmetricKey: string, publicKey: string): string {
        const buffer = Buffer.from(symmetricKey, 'hex');
        const encrypted = crypto.publicEncrypt(publicKey, buffer);
        return encrypted.toString('hex');
      }

}
