import { ChatService } from './services/chat.service';
import { Chat<PERSON>ontroller } from './controllers/chat.controller';
/*
https://docs.nestjs.com/modules
*/

import { Module, forwardRef } from '@nestjs/common';
import { ChatGateway } from './services/chat.gateway';
import { MongooseModule } from '@nestjs/mongoose';
import { UserModule } from 'src/user/user.module';
import { FileModule } from 'src/file/file.module';
import { LogModule } from 'src/log/log.module';
import { MailModule } from 'src/mail/mail.module';
import { Chat, ChatSchema } from 'src/models/chat.model';
import { ChatMessageService } from './services/chat-message.service';
import { ChatMessage, ChatMessageSchema } from 'src/models/chat-message.model';
import { ChatMessageController } from './controllers/chat-message.controller';
import { JwtService } from '@nestjs/jwt';
import { ModuleRef } from '@nestjs/core';
import { MessageScheduleService } from './services/message-schedule.service';
import { MessageScheduleController } from './controllers/message-schedule.controller';
import { JwtStrategy } from './jwt.strategy';
import { PassportModule } from '@nestjs/passport';
import { ScheduledMessage, ScheduledMessageSchema } from 'src/call/model/schedule_message';
import { ChatInviteService } from './services/chat-invite.service';
import { ChatInvite, ChatInviteSchema } from 'src/call/model/chat-invite';
import { ChatActivity, ChatActivitySchema } from 'src/models/chat_activity_response.model';
import { ActivityController } from './controllers/activity.controller';
import { ActivityService } from './services/activity.service';
import { IsFutureDateTimeConstraint } from './validators/is_future_date_time.validator';
import { SearchController } from './controllers/search.controller';
import { SearchService } from './services/search.service';
import { RedisService } from './redis.service';
import { WorkcircleController } from './controllers/workcircle.controller';
import { WorkCircleService } from './services/work-circle.service';
import { LiveChatModule } from 'src/live-chat/live-chat.module';
import { DraftSchema, Draft } from 'src/models/draft.model';
import { DraftController } from './controllers/draft.controller';
import { DraftService } from './services/draft.service';
import { HttpmessageService } from './services/httpmessage.service';
import { CallNotificationService } from './services/call-notification.service';
import { RabbitmqModule } from 'src/rabbitmq/rabbitmq.module';

@Module({
    imports: [
        forwardRef(() => UserModule),
        forwardRef(() => LiveChatModule,),
        FileModule,
        LogModule,
        MailModule,
        PassportModule,
        RabbitmqModule,
        MongooseModule.forFeature([
            { name: Chat.name, schema: ChatSchema },
            { name: ChatMessage.name, schema: ChatMessageSchema },
            { name: ScheduledMessage.name, schema: ScheduledMessageSchema },
            { name: ChatInvite.name, schema: ChatInviteSchema },
            { name: ChatActivity.name, schema: ChatActivitySchema },
            { name: Draft.name, schema: DraftSchema }
        ])],
    controllers: [
        DraftController,
        ChatController,
        ChatMessageController,
        MessageScheduleController,
        ActivityController,
        SearchController, WorkcircleController
    ],
    providers: [HttpmessageService,
        DraftService,
        RedisService,
        ChatService,
        ChatMessageService,
        ChatGateway,
        ActivityService,
        JwtService,
        MessageScheduleService,
        IsFutureDateTimeConstraint,
        JwtStrategy,
        SearchService,
        ChatInviteService,
        WorkCircleService,
        CallNotificationService],
    exports: [
        ChatService,
        ChatGateway,
        ChatMessageService,
        JwtStrategy,
        ActivityService]
})
export class ChatModule { }
