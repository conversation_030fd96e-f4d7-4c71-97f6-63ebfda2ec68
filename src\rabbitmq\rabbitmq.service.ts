import { Injectable, Inject, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { catchError, lastValueFrom, of, tap, timeout } from 'rxjs';
import { Socket } from 'socket.io';
import { LogService } from 'src/log/log.service';

@Injectable()
export class RabbitMQService {
    queues = ['user-created-queue', 'notification-message-queue', 'http-message']
    events = ['workspace-created-event', 'notification-message-event', 'http-message']

    // Queue configurations with TTL settings (corrected values)
    private queueConfigurations = {
        'user-created-queue': {
            // 100 hours = 100 * 60 * 60 * 1000 = 360,000,000 ms
            expiration: 360000000, // 100 hours (corrected)
            args: {
                'x-message-ttl': 360000000
            }
        },
        'notification-message-queue': {
            expiration: 360000000, // 100 hours (corrected)
            args: {
                'x-message-ttl': 360000000
            }
        },
        'http-message': {
            expiration: null,
            args: {}
        }
    }

    constructor(
        @Inject('USER_SERVICE') private readonly userClient: ClientProxy,
        @Inject('NOTIFICATION_SERVICE') private readonly notificationClient: ClientProxy,
        @Inject('HTTP_MESSAGE_SERVICE') private readonly httpMessageClient: ClientProxy,
        private logService: LogService,
        private configService: ConfigService
    ) {}

    /**
     * Get queue configuration including TTL settings
     */
    getQueueConfiguration(queueName: string) {
        return this.queueConfigurations[queueName] || { expiration: null, args: {} };
    }

    /**
     * Get the appropriate client proxy based on queue name
     */
    private getClientForQueue(queueName: string): ClientProxy {
        switch (queueName) {
            case 'user-created-queue':
                return this.userClient;
            case 'notification-message-queue':
                return this.notificationClient;
            case 'http-message':
                return this.httpMessageClient;
            default:
                return this.userClient;
        }
    }

    async onModuleInit() {
        // Connect all clients with proper error handling
        try {
            await Promise.all([
                this.userClient.connect().catch(err => {
                    this.logService.error('User RabbitMQ connect Error: ', err.message);
                    throw err;
                }),
                this.notificationClient.connect().catch(err => {
                    this.logService.error('Notification RabbitMQ connect Error: ', err.message);
                    throw err;
                }),
                this.httpMessageClient.connect().catch(err => {
                    this.logService.error('HTTP Message RabbitMQ connect Error: ', err.message);
                    throw err;
                })
            ]);

            this.logService.log('All RabbitMQ clients connected successfully');
            
            // Log queue configurations
            for (const queue of this.queues) {
                const config = this.getQueueConfiguration(queue);
                this.logService.log(`Queue configured: ${queue}`, 
                    config.expiration ? `with TTL: ${config.expiration}ms` : 'without TTL');
            }
        } catch (error) {
            this.logService.error('Failed to initialize RabbitMQ connections:', error);
            throw error;
        }
    }

    /**
     * Send notification to external consumers (fire-and-forget)
     * External consumers don't send responses, so we use emit()
     */
    async sendNotification(queue: string, message: string, authToken: string, socket: Socket) {
        if (!this.queues.includes(queue)) {
            throw new Error(`Queue ${queue} is not configured`);
        }

        const client = this.getClientForQueue(queue);
        this.logService.log('Sending notification to external consumer via queue: ', queue);

        try {
            // Use emit() for external consumers - they don't send responses
            client.emit(queue, message);
            console.log(`Message successfully emitted to ${queue} for external consumer`);
            
            return { 
                success: true, 
                method: 'rabbitmq-emit',
                queue: queue,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            this.logService.error(`RabbitMQ emit failed for ${queue}: ${error.message}`);
            this.logService.log('RabbitMQ failed, falling back to HTTP request...');
            return await this.sendNotificationFallbackRequest(queue, message, authToken, socket);
        }
    }

    /**
     * Send notification with response verification - NOT RECOMMENDED for external consumers
     * This will timeout since external consumers don't send responses
     */
    async sendNotificationWithResponse(queue: string, message: string, authToken: string, socket: Socket) {
        this.logService.warn(`sendNotificationWithResponse called for ${queue} - this will likely timeout with external consumers`);
        
        if (!this.queues.includes(queue)) {
            throw new Error(`Queue ${queue} is not configured`);
        }

        const client = this.getClientForQueue(queue);
        this.logService.log('Attempting to send notification with response to external consumer (not recommended): ', queue);

        try {
            const response = await lastValueFrom(
                client.send(queue, message).pipe(
                    timeout(2000), // Very short timeout since external consumers won't respond
                    catchError((error) => {
                        this.logService.log(`Expected timeout for external consumer ${queue}: ${error.message}`);
                        return of(null);
                    })
                )
            );

            // External consumers likely won't respond, so fallback immediately
            this.logService.log(`No response from external consumer ${queue} (expected), using fallback`);
            return await this.sendNotificationFallbackRequest(queue, message, authToken, socket);
            
        } catch (error) {
            this.logService.log(`Expected failure for external consumer ${queue}, using fallback`);
            return await this.sendNotificationFallbackRequest(queue, message, authToken, socket);
        }
    }

    /**
     * Fire-and-forget notification using emit()
     */
    emitNotification(queue: string, message: string) {
        if (!this.queues.includes(queue)) {
            throw new Error(`Queue ${queue} is not configured`);
        }

        const client = this.getClientForQueue(queue);
        this.logService.log('Emitting to queue: ', queue);
        
        // emit() is fire-and-forget, no response expected
        client.emit(queue, message);
    }

    async sendNotificationFallbackRequest(queue: string, message: string, authToken: string, socket: Socket) {
        try {
            const host: string = socket.handshake.headers.host as string;
            //const url = 'https://' + host.replace('chats', 'api') + '/notify/api/Notification/Send';
            
            const url = this.configService.get('JOBLE_API') + 'Notification/Send';
            this.logService.log('Fallback API URL: ', url);

            const response = await fetch(url, {
                method: 'POST',
                body: typeof message === 'string' ? message : JSON.stringify(message),
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + authToken
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            this.logService.log('Fallback notification response: ', result);
            return result;

        } catch (error) {
            this.logService.error('Error in fallback notification: ', error.message);
            throw error;
        }
    }

    /**
     * Send message to external consumers (fire-and-forget)
     */
    async sendMessageWithOutResponse(queue: string, message: string) {
        if (!this.queues.includes(queue)) {
            throw new Error(`Queue ${queue} is not configured`);
        }

        const client = this.getClientForQueue(queue);
        this.logService.log('Sending message to external consumer via queue: ', queue);

        try {
            // External consumers don't send responses, use emit()
            client.emit(queue, message);
            this.logService.log(`Message successfully sent to ${queue}`);
            return { 
                success: true, 
                method: 'emit',
                queue: queue,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logService.error(`Failed to send message to ${queue}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Only use this if you have internal NestJS microservices that send responses
     * External consumers won't work with this method
     */
    async sendMessage(queue: string, message: string, timeoutMs: number = 5000) {
        if (!this.queues.includes(queue)) {
            throw new Error(`Queue ${queue} is not configured`);
        }

        const client = this.getClientForQueue(queue);
        this.logService.log('Sending message with response expectation to queue: ', queue);
        this.logService.warn('Note: This will timeout if consumer is external and does not send responses');

        try {
            const response = await lastValueFrom(
                client.send(queue, message).pipe(
                    timeout(timeoutMs),
                    catchError((error) => {
                        this.logService.error(`Timeout/Error for ${queue} (likely external consumer): ${error.message}`);
                        return of({ timeout: true, error: error.message });
                    })
                )
            );

            this.logService.log('Queue response: ', response);
            return response;
        } catch (error) {
            this.logService.error(`Failed to send message with response to ${queue}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Only use this if you have internal NestJS microservices that sends responses but you want to wait for the response
     * @param queue 
     * @param message 
     */
    async sendMessageWithSubscriber(queue: string, message: string) {
    if (!this.queues.includes(queue)) {
      throw new Error(`Queue ${queue} is not configured`);
    }
    this.logService.log('Queuing: ', queue, message);

    const client = this.getClientForQueue(queue);
    client
      .send(queue, message)
      .subscribe((res) => this.logService.log('Queue response: ', res));
    }

    async receiveMessage(queue: string, callback: (message: any) => void) {
        this.logService.log('Setting up message receiver for queue: ', queue);
        this.logService.warn('Note: receiveMessage should not be used in producer services with external consumers');
        
        if (!this.queues.includes(queue)) {
            throw new Error(`Queue ${queue} is not configured`);
        }

        // For external consumers, you typically don't receive messages in the same service
        // This would be handled by the external consumer applications
        this.logService.error('receiveMessage not implemented for external consumer pattern');

        const client = this.getClientForQueue(queue);
        client.send({ cmd: queue }, {}).subscribe(callback);
    }

    /**
     * Check if RabbitMQ connections are healthy
     */
    async checkConnectionHealth(): Promise<{ [key: string]: boolean }> {
        const health = {
            userClient: false,
            notificationClient: false,
            httpMessageClient: false
        };

        try {
            // Simple ping test - emit a health check message
            this.userClient.emit('health-check', { timestamp: Date.now() });
            health.userClient = true;
        } catch (error) {
            this.logService.error('User client health check failed:', error.message);
        }

        try {
            this.notificationClient.emit('health-check', { timestamp: Date.now() });
            health.notificationClient = true;
        } catch (error) {
            this.logService.error('Notification client health check failed:', error.message);
        }

        try {
            this.httpMessageClient.emit('health-check', { timestamp: Date.now() });
            health.httpMessageClient = true;
        } catch (error) {
            this.logService.error('HTTP message client health check failed:', error.message);
        }

        this.logService.log('Connection health check:', health);
        return health;
    }
}