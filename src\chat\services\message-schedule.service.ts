/*
https://docs.nestjs.com/providers#services
*/

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { ScheduledMessageDto } from 'src/call/dto/schedule_message.dto';
import { UpdateMessageScheduleDto } from 'src/call/dto/schedule_message.dto';
import { ScheduledMessage } from 'src/call/model/schedule_message';
import { LogService } from 'src/log/log.service';
import { ChatUser } from 'src/models/chat-user.model';

@Injectable()
export class MessageScheduleService {

    constructor(
        @InjectModel(ScheduledMessage.name) private readonly scheduleMessageModel: Model<ScheduledMessage>,
        private logService: LogService) { }
    async findDueSchedules() {
        const dueSchedules = await this.scheduleMessageModel.find().sort({ dueDate: 1 })
        return dueSchedules;
    }
    async getUserSchedules(user: ChatUser) {
        return await this.scheduleMessageModel.find({ userId: user.id })
            .populate({
                path: 'chatId', model: 'Chat', select: ' -jobProUserIds', populate: {
                    path: 'users',
                    model: 'ChatUser'
                }
            })

            .populate({ path: 'userId', model: 'ChatUser' });
    }
    async scheduleMessage(messageSchedule: ScheduledMessageDto, user: ChatUser) {
        this.logService.log('date: ', new Date().toDateString())
        try {
            const toSave = await this.scheduleMessageModel.create({ ...messageSchedule, userId: user.id, jobProUserId: user.jobProUserId });
            const saved = toSave.populate({
                path: 'chatId', model: 'Chat', select: '-jobProUserIds', populate: {
                    path: 'users',
                    model: 'ChatUser'
                }
            });
            return (await saved).toJSON()
        } catch (error) {
            this.logService.log('Error scheduling message: ', error.message);
            const message = error.message.toString().toLowerCase().includes('due date') ? 'Due date and time can not be in the past' : error.message;
            throw { ...error, message: message };
        }

    }
    async editScheduledMessage(id: string, messageSchedule: UpdateMessageScheduleDto) {
        this.logService.log('comming soon: ', id, messageSchedule);
        const found = await this.scheduleMessageModel.findOne({ _id: id });
        if (!found) throw new NotFoundException('No scheduled message has the supplied id');
        await this.scheduleMessageModel.updateOne({ _id: id }, messageSchedule);
        return await this.findById(id)
    }
    async findById(id: string) {
        const found = await this.scheduleMessageModel.findOne({ _id: id })
            .populate({ select: '-users -jobProUserIds', path: 'chatId', model: 'Chat' })
            .populate({ path: 'chatId.users', model: 'ChatUser' })
            .populate({
                path: 'userId', model: 'ChatUser'
            });
        return found.toJSON()
    }
    async deleteSchedule(id: string) {
        const res = await this.scheduleMessageModel.deleteOne({ _id: id });
        if (res.deletedCount) return { message: 'delete successful' }
        return { message: 'delete failed' }
    }
}
