// import {
//     ConnectedSocket,
//     MessageBody,
//     SubscribeMessage,
//     WebSocketGateway,
//     WebSocketServer,
// } from '@nestjs/websockets';
// import { createWorker, types } from 'mediasoup';
// import { MediaKind } from 'mediasoup//node/lib/RtpParameters';
// import { RouterOptions } from 'mediasoup/node/lib/Router';
// import { Transport } from 'mediasoup/node/lib/types';
// import { Server, Socket } from 'socket.io';
// @WebSocketGateway({
//     cors: {
//         origin: '*',
//     },
// })
// export class CallGateway {
//     @WebSocketServer()
//     server: Server;
//     worker: types.Worker;
//     router: types.Router;
//     rtpParameters: types.RtpParameters;
//     rooms = {}          // { roomName1: { Router, rooms: [ sicketId1, ... ] }, ...}
//     peers = {}          // { socketId1: { roomName1, socket, transports = [id1, id2,] }, this.producers = [id1, id2,] }, consumers = [id1, id2,], peerDetails }, ...}
//     transports = []     // [ { socketId1, roomName1, transport, consumer }, ... ]
//     producers = []      // [ { socketId1, roomName1, producer, }, ... ]
//     consumers = []      // [ { socketId1, roomName1, consumer, }, ... ]

//     mediaCodecs = [
//         {
//             kind: 'audio' as MediaKind,
//             mimeType: 'audio/opus',
//             clockRate: 48000,
//             channels: 2,
//         },
//         {
//             kind: 'video' as MediaKind,
//             mimeType: 'video/H264',
//             clockRate: 90000,
//             parameters: {
//                 'packetization-mode': 1,
//                 'profile-level-id': '42e01f',
//                 'level-asymmetry-allowed': 1,
//             },
//         },
//     ];

//     constructor() {
//         this.createWork();
//     }
//     async createWork() {
//         this.worker = await createWorker();
//         this.router = await this.worker.createRouter({
//             mediaCodecs: this.mediaCodecs,
//         } as RouterOptions);
//         this.logService.log('CallGateWay active: ', this.worker.pid);
//         this.worker.on('died', (err) => {
//             this.logService.log('mediasoup worker has died');
//             setTimeout(() => {
//                 process.exit(1);
//             }, 2000);
//         });
//         // return this.worker;
//     }
//     @SubscribeMessage('join-hubspot')
//     joinHubspot(@ConnectedSocket() socket: Socket, @MessageBody() data: any) {
//         this.logService.log('data: ', data);
//         this.logService.log('socket: ', socket.id);
//     }
//     @SubscribeMessage('getRtpCapabilities')
//     getCapabilities(@ConnectedSocket() socket: Socket) {
//         const rtpCapabilities = this.router.rtpCapabilities;
//         this.logService.log('rtpCapabilities: ', socket.id, rtpCapabilities);
//         this.server.to(socket.id).emit('getRtpCapabilities', { rtpCapabilities });

//         // see client's socket.emit('transport-connect', ...)
//         socket.on('transport-connect', ({ dtlsParameters }) => {
//             this.logService.log('DTLS PARAMS... ', { dtlsParameters })

//             this.getTransport(socket.id).connect({ dtlsParameters })
//         })

//         // see client's socket.emit('transport-produce', ...)
//         socket.on('transport-produce', async ({ kind, rtpParameters, appData }, callback) => {
//             // call produce based on the prameters from the client
//             const producer = await this.getTransport(socket.id).produce({
//                 kind,
//                 rtpParameters,
//             })

//             // add producer to the this.producers array
//             // if(this.peers[socket.id])
//             const { roomName } = this.peers[socket.id]

//             this.addProducer(producer, roomName, socket.id)

//             this.informConsumers(roomName, socket.id, producer.id)

//             this.logService.log('Producer ID: ', producer.id, producer.kind)

//             producer.on('transportclose', () => {
//                 this.logService.log('transport for this producer closed ')
//                 producer.close()
//             })

//             // Send back to the client the Producer's id
//             callback({
//                 id: producer.id,
//                 producersExist: this.producers.length > 1 ? true : false
//             })
//         })

//         // see client's socket.emit('transport-recv-connect', ...)
//         socket.on('transport-recv-connect', async ({ dtlsParameters, serverConsumerTransportId }) => {
//             this.logService.log(`DTLS PARAMS: ${dtlsParameters}`)
//             const consumerTransport = this.transports.find(transportData => (
//                 transportData.consumer && transportData.transport.id == serverConsumerTransportId
//             )).transport
//             await consumerTransport.connect({ dtlsParameters })
//         })

//         socket.on('consume', async ({ rtpCapabilities, remoteProducerId, serverConsumerTransportId }, callback) => {
//             try {

//                 const { roomName } = this.peers[socket.id]
//                 const router = this.rooms[roomName].router
//                 let consumerTransport = this.transports.find(transportData => (
//                     transportData.consumer && transportData.transport.id == serverConsumerTransportId
//                 )).transport

//                 // check if the router can consume the specified producer
//                 if (router.canConsume({
//                     producerId: remoteProducerId,
//                     rtpCapabilities
//                 })) {
//                     // transport can now consume and return a consumer
//                     const consumer = await consumerTransport.consume({
//                         producerId: remoteProducerId,
//                         rtpCapabilities,
//                         paused: true,
//                     })

//                     consumer.on('transportclose', () => {
//                         this.logService.log('transport close from consumer')
//                     })

//                     consumer.on('producerclose', () => {
//                         this.logService.log('producer of consumer closed')
//                         socket.emit('producer-closed', { remoteProducerId })

//                         consumerTransport.close([])
//                         this.transports = this.transports.filter(transportData => transportData.transport.id !== consumerTransport.id)
//                         consumer.close()
//                         this.consumers = this.consumers.filter(consumerData => consumerData.consumer.id !== consumer.id)
//                     })

//                     this.addConsumer(consumer, roomName, socket.id)

//                     // from the consumer extract the following params
//                     // to send back to the Client
//                     const params = {
//                         id: consumer.id,
//                         producerId: remoteProducerId,
//                         kind: consumer.kind,
//                         rtpParameters: consumer.rtpParameters,
//                         serverConsumerId: consumer.id,
//                     }

//                     // send the parameters to the client
//                     callback({ params })
//                 }
//             } catch (error) {
//                 this.logService.log(error.message)
//                 callback({
//                     params: {
//                         error: error
//                     }
//                 })
//             }
//         })

//         socket.on('consumer-resume', async ({ serverConsumerId }) => {
//             this.logService.log('consumer resume')
//             const { consumer } = this.consumers.find(consumerData => consumerData.consumer.id === serverConsumerId)
//             await consumer.resume()
//         })
//         socket.on('disconnect', () => {
//             try {
//                 // do some cleanup
//                 this.logService.log('peer disconnected')
//                 this.consumers = this.removeItems(this.consumers, socket.id, 'consumer')
//                 this.producers = this.removeItems(this.producers, socket.id, 'producer')
//                 this.transports = this.removeItems(this.transports, socket.id, 'transport')
//                 if (this.peers[socket.id]) {
//                     const { roomName } = this.peers[socket.id]
//                     delete this.peers[socket.id]

//                     // remove socket from room
//                     this.rooms[roomName] = {
//                         router: this.rooms[roomName].router,
//                         peers: this.rooms[roomName].peers.filter(socketId => socketId !== socket.id)
//                     }
//                 }
//             } catch (error) {
//                 this.logService.log('error on socket disconnect: ', error)
//             }
//         })

//         socket.on('joinRoom', async ({ roomName, name, isAdmin }, callback) => {
//             // create Router if it does not exist
//             // const router1 = rooms[roomName] && rooms[roomName].get('data').router || await createRoom(roomName, socket.id)
//             const router1 = await this.createRoom(roomName, socket.id)

//             this.peers[socket.id] = {
//                 socket,
//                 roomName,           // Name for the Router this Peer joined
//                 transports: [],
//                 producers: [],
//                 consumers: [],
//                 peerDetails: {
//                     name,
//                     isAdmin: isAdmin ? isAdmin : false,   // Is this Peer the Admin?
//                 }
//             }

//             // get Router RTP Capabilities
//             const rtpCapabilities = router1.rtpCapabilities

//             // call callback from the client and send back the rtpCapabilities
//             callback({ rtpCapabilities })
//         })
//         // socket.on('createRoom', async (callback) => {
//         //   if (router === undefined) {
//         //     // worker.createRouter(options)
//         //     // options = { mediaCodecs, appData }
//         //     // mediaCodecs -> defined above
//         //     // appData -> custom application data - we are not supplying any
//         //     // none of the two are required
//         //     router = await worker.createRouter({ mediaCodecs, })
//         //     this.logService.log(`Router ID: ${router.id}`)
//         //   }

//         //   getRtpCapabilities(callback)
//         // })

//         // const getRtpCapabilities = (callback) => {
//         //   const rtpCapabilities = router.rtpCapabilities

//         //   callback({ rtpCapabilities })
//         // }

//         // Client emits a request to create server side Transport
//         // We need to differentiate between the producer and consumer transports
//         socket.on('createWebRtcTransport', async ({ consumer }, callback) => {
//             // get Room Name from Peer's properties
//             const roomName = this.peers[socket.id].roomName

//             // get Router (Room) object this peer is in based on RoomName
//             const router = this.rooms[roomName].router

//             this.createWebRtcTransport(router).then(
//                 (transport: any) => {
//                     callback({
//                         params: {
//                             id: transport.id,
//                             iceParameters: transport.iceParameters,
//                             iceCandidates: transport.iceCandidates,
//                             dtlsParameters: transport.dtlsParameters,
//                         }
//                     })

//                     // add transport to Peer's properties
//                     this.addTransport(transport, roomName, consumer, socket.id)
//                 },
//                 error => {
//                     this.logService.log(error)
//                 })
//         })

//         socket.on('getProducers', callback => {
//             //return all producer transports
//             const { roomName } = this.peers[socket.id]

//             let producerList = []
//             this.producers.forEach(producerData => {
//                 if (producerData.socketId !== socket.id && producerData.roomName === roomName) {
//                     producerList = [...producerList, producerData.producer.id]
//                 }
//             })

//             // return the producer list back to the client
//             callback(producerList)
//         })
//     }

//     removeItems(items, socketId, type) {
//         items.forEach(item => {
//             if (item.socketId === socketId) {
//                 item[type].close()
//             }
//         })
//         items = items.filter(item => item.socketId !== socketId)

//         return items
//     }

//     // handleDisconnect(@ConnectedSocket() socket: Socket) {

//     //     // do some cleanup
//     //     this.logService.log('peer disconnected')
//     //     this.consumers = this.removeItems(this.consumers, socket.id, 'consumer')
//     //     this.this.producers = this.removeItems(this.this.producers, socket.id, 'producer')
//     //     this.transports = this.removeItems(this.transports, socket.id, 'transport')

//     //     const mydata = this.this.peers[socket.id];
//     //     if (mydata) {
//     //         let { roomName } = mydata;

//     //         delete this.this.peers[socket.id]

//     //         // remove socket from room
//     //         this.rooms[roomName] = {
//     //             router: this.rooms[roomName].router,
//     //             this.peers: this.rooms[roomName].this.peers.filter(socketId => socketId !== socket.id)
//     //         }
//     //     }
//     // }
//     // @SubscribeMessage('joinRoom')
//     // async joinRoom(@ConnectedSocket() socket: Socket, @MessageBody() data: { roomName: string, participantName?: string }, callback: any) {
//     //     // create Router if it does not exist
//     //     // const router1 = rooms[roomName] && rooms[roomName].get('data').router || await createRoom(roomName, socket.id)
//     //     const router1 = await this.createRoom(data.roomName, socket.id)

//     //     this.this.peers[socket.id] = {
//     //         socket,
//     //         roomName: data.roomName,   // Name for the Router this Peer joined
//     //         name: data.participantName,
//     //         transports: [],
//     //         this.producers: [],
//     //         consumers: [],
//     //         peerDetails: {
//     //             name: '',
//     //             isAdmin: false,   // Is this Peer the Admin?
//     //         }
//     //     }

//     //     // get Router RTP Capabilities
//     //     const rtpCapabilities = router1.rtpCapabilities;
//     //     socket.emit('joinedRoom', rtpCapabilities);

//     //     // call callback from the client and send back the rtpCapabilities
//     //     callback({ rtpCapabilities })
//     //     return { rtpCapabilities };
//     // }

//     async createRoom(roomName: string, socketId: string) {
//         // worker.createRouter(options)
//         // options = { mediaCodecs, appData }
//         // mediaCodecs -> defined above
//         // appData -> custom application data - we are not supplying any
//         // none of the two are required
//         let router1;
//         let peers = []
//         if (this.rooms[roomName]) {
//             router1 = this.rooms[roomName].router
//             peers = this.rooms[roomName].this.peers || []
//         } else {
//             // router1 = await this.worker.createRouter({ mediaCodecs: this.mediaCodecs, })
//         }

//         this.logService.log(`Router ID: ${router1.id}`, peers.length)

//         this.rooms[roomName] = {
//             router: router1,
//             peers: [...peers, socketId],
//         }

//         return router1
//     }

//     addTransport = (transport: any, roomName: string, consumer: any, socketId: string) => {

//         this.transports = [
//             ...this.transports,
//             { socketId: socketId, transport, roomName, consumer, }
//         ]

//         this.peers[socketId] = {
//             ...this.peers[socketId],
//             transports: [
//                 ...this.peers[socketId].transports,
//                 transport.id,
//             ]
//         }
//     }

//     addProducer = (producer: any, roomName: string, socketId: string) => {
//         this.producers = [
//             ...this.producers,
//             { socketId: socketId, producer, roomName, }
//         ]

//         this.peers[socketId] = {
//             ...this.peers[socketId],
//             producers: [
//                 ...this.peers[socketId].producers,
//                 producer.id,
//             ]
//         }
//     }

//     addConsumer = (consumer: any, roomName: string, socketId: string) => {
//         // add the consumer to the consumers list
//         this.consumers = [
//             ...this.consumers,
//             { socketId: socketId, consumer, roomName, }
//         ]

//         // add the consumer id to the this.peers list
//         this.peers[socketId] = {
//             ...this.peers[socketId],
//             consumers: [
//                 ...this.peers[socketId].consumers,
//                 consumer.id,
//             ]
//         }
//     }

//     informConsumers = (roomName: string, socketId: string, id: string) => {
//         this.logService.log(`just joined, id ${id} ${roomName}, ${socketId}`)
//         // A new producer just joined
//         // let all consumers to consume this producer
//         this.producers.forEach(producerData => {
//             if (producerData.socketId !== socketId && producerData.roomName === roomName) {
//                 const producersocket = this.peers[producerData.socketId].socket
//                 // use socket to send producer id to producer
//                 producersocket.emit('new-producer', { producerId: id })
//             }
//         })
//     }

//     getTransport = (socketId) => {
//         const [producerTransport] = this.transports.filter(transport => transport.socketId === socketId && !transport.consumer)
//         return producerTransport.transport
//     }

//     createWebRtcTransport = async (router) => {
//         return new Promise(async (resolve, reject) => {
//             try {
//                 // https://mediasoup.org/documentation/v3/mediasoup/api/#WebRtcTransportOptions
//                 const webRtcTransport_options = {
//                     listenIps: [
//                         {
//                             ip: '0.0.0.0', // replace with relevant IP address
//                             announcedIp: ['***********', '*************'],
//                         }
//                     ],
//                     enableUdp: true,
//                     enableTcp: true,
//                     preferUdp: true,
//                 }

//                 // https://mediasoup.org/documentation/v3/mediasoup/api/#router-createWebRtcTransport
//                 let transport = await router.createWebRtcTransport(webRtcTransport_options)
//                 this.logService.log(`transport id: ${transport.id}`)

//                 transport.on('dtlsstatechange', dtlsState => {
//                     if (dtlsState === 'closed') {
//                         transport.close()
//                     }
//                 })

//                 transport.on('close', () => {
//                     this.logService.log('transport closed')
//                 })

//                 resolve(transport)

//             } catch (error) {
//                 reject(error)
//             }
//         })
//     }
// }
// // key: APIP7RqXg2P43RE
// // secret: GkfIdi6AC4CNrx9eajsp9A94SLa1QugNby7BeRJl0FWA
