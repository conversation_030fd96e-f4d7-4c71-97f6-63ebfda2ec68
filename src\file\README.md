# File Service - Multi-Cloud Storage Implementation

This module provides file upload, download, and management functionality supporting both Google Cloud Storage (GCS) and AWS S3 as storage backends.

## Files

- `awsservice.ts` - Core AWS S3 service implementation
- `file.service.ts` - Main file service that uses AWS S3 service
- `file.controller.ts` - REST API endpoints for file operations
- `file.module.ts` - NestJS module configuration

## Environment Variables

Add these environment variables to your `.env` file:

### For AWS S3:
```env
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=your-s3-bucket-name
```

### For Google Cloud Storage:
```env
GCS_BUCKET_NAME=your-gcs-bucket-name
# Also ensure service-account-key.json is in the project root
```

## API Endpoints

### AWS S3 Endpoints

#### Upload File to S3
- **POST** `/v1/file/s3upload`
- Upload a file to S3
- Returns file information including signed URL

#### Get File from S3
- **GET** `/v1/file/s3/:filename`
- Download a file from S3
- Streams the file directly to the response

#### Get S3 File URL
- **GET** `/v1/file/s3/url/:filename`
- Get a signed URL for a file (1 year expiration)
- Returns: `{ url: "signed_url" }`

#### Delete S3 File
- **DELETE** `/v1/file/s3/:filename`
- Delete a file from S3
- Returns: `{ success: boolean, message: string }`

#### S3 Health Check
- **GET** `/v1/file/health/s3`
- Check S3 connection status
- Returns: `{ status: "healthy|unhealthy", service: "AWS S3", timestamp: "ISO_date" }`

### Google Cloud Storage Endpoints

#### Upload File to GCS (Legacy)
- **POST** `/v1/file/s3upload` (uses gcsUpload method)
- Upload a file to GCS
- Returns file information including signed URL

#### Get File from GCS
- **GET** `/v1/file/:filename`
- Download a file from GCS
- Streams the file directly to the response

#### Get GCS File URL
- **GET** `/v1/file/gcs/url/:filename`
- Get a signed URL for a GCS file (1 year expiration)
- Returns: `{ url: "signed_url" }`

## Features

- **Multi-Cloud Support**: Supports both AWS S3 and Google Cloud Storage
- **Audio Conversion**: Automatically converts audio files to WAV format using ffmpeg
- **Unique Filenames**: Generates unique filenames to prevent conflicts
- **Signed URLs**: Provides secure, time-limited access to files
- **Error Handling**: Comprehensive error handling and logging
- **Dual Implementation**: Both storage backends available simultaneously

## Usage Examples

### AWS S3 Usage
```typescript
import { FileService } from './file.service';

// Upload a file to S3
const fileInfo = await fileService.s3Upload(file);

// Get a signed URL from S3
const url = await fileService.getS3FilePublicUrl(filename);

// Delete a file from S3
const success = await fileService.deleteFromS3(filename);
```

### Google Cloud Storage Usage
```typescript
import { FileService } from './file.service';

// Upload a file to GCS
const fileInfo = await fileService.gcsUpload(file);

// Get a signed URL from GCS
const url = await fileService.getGCSFilePublicUrl(filename);

// Get file from GCS (streaming)
fileService.getFromGCS(filename, res);
```

## Dependencies

- `@aws-sdk/client-s3` - AWS S3 client
- `@aws-sdk/s3-request-presigner` - For generating signed URLs
- `@google-cloud/storage` - Google Cloud Storage client
- `fluent-ffmpeg` - For audio file conversion

## Storage Backend Selection

Both storage backends are available simultaneously. Choose the appropriate endpoints based on your needs:
- Use `/s3/` prefixed endpoints for AWS S3 operations
- Use `/gcs/` prefixed endpoints or legacy endpoints for Google Cloud Storage operations
