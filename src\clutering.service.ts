// clustering.service.ts

import cluster from 'node:cluster';
import { availableParallelism, cpus } from 'node:os';
import process from 'node:process';
import { ConfigService } from '@nestjs/config';
import { LogService } from './log/log.service';

export class ClusteringService {
    constructor(private configService: ConfigService, private logService: LogService) { }

    static startClusteredApp(appBootstrap: () => Promise<void>) {
        const numCPUs = cpus().length;
        const env = process.env.ENVIRONMENT;

        if (!env || env.toLocaleLowerCase().includes('prod')) {
            if (cluster.isPrimary) {
                console.log(`Primary ${process.pid} is running`);

                // Fork workers for each CPU core.
                for (let i = 0; i < numCPUs; i++) {
                    cluster.fork();
                }

                cluster.on('exit', (worker, code, signal) => {
                    console.warn(`Worker ${worker.process.pid} died. Restarting...`);
                    cluster.fork(); // Restart the worker on failure.
                });

                // Global error handling in primary process.
                process.on('unhandledRejection', (reason, promise) => {
                    console.error('Unhandled Rejection:', reason);
                });

                process.on('uncaughtException', (error) => {
                    console.error('Uncaught Exception:', error);
                    process.exit(1);
                });

                // Graceful shutdown for the primary process
                process.on('SIGTERM', () => {
                    console.log('Primary process is shutting down gracefully...');
                    // Optionally close any open connections or cleanup here
                    process.exit(0);
                });

                process.on('SIGINT', () => {
                    console.log('Primary process is shutting down gracefully...');
                    process.exit(0);
                });

            } else {
                // Workers run the application bootstrap.
                appBootstrap().then(() => {
                    console.log(`Worker ${process.pid} started`);
                }).catch(err => {
                    console.error(`Worker ${process.pid} failed to start`, err);
                    process.exit(1); // Exit if the app fails to start in a worker.
                });

                // Global error handling in worker process.
                process.on('unhandledRejection', (reason, promise) => {
                    console.error(`Worker ${process.pid} Unhandled Rejection:`, reason);
                });

                process.on('uncaughtException', (error) => {
                    console.error(`Worker ${process.pid} Uncaught Exception:`, error);
                    process.exit(1);
                });

                // Graceful shutdown for the worker process
                const shutdown = () => {
                    console.log(`Worker ${process.pid} is shutting down gracefully...`);
                    // Perform cleanup tasks like closing DB connections
                    appBootstrap().then(() => {
                        process.exit(0);
                    }).catch(err => {
                        console.error(`Error during worker shutdown: ${err}`);
                        process.exit(1);
                    });
                };

                process.on('SIGTERM', shutdown);
                process.on('SIGINT', shutdown);
            }
        } else {
            appBootstrap();
        }
    }
}
