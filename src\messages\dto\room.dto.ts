/* eslint-disable prettier/prettier */
import { PartialType } from "@nestjs/mapped-types";
import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsDate, IsOptional, IsString } from "class-validator";
import { UserDto } from "src/user/dto/user.dto";
import { MessageDto } from "./message.dto";

export class RoomDto {

    name: string;
    description?: string;
    tenantId: string;
    users?: UserDto[] | string[];
    createdAt: Date;
    updatedAt: Date;
    messages?: MessageDto[];
    id: string;
    isArchived: boolean;
    archivedBy?: UserDto | string;
    _id?: string;
    subdomain: string;
}

export class UpdateRoomDto extends PartialType(RoomDto) { }