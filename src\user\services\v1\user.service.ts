import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateUserDto } from '../../dto/create-user.dto';
import { UpdateUserDto } from '../../dto/update-user.dto';
import { UserDto } from '../../dto/user.dto';

import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { MessageDto } from 'src/messages/dto/message.dto';
import { JobleUserDto } from '../../dto/joble-user.dto';
import { LogService } from 'src/log/log.service';

const userEndpoint = 'identity/getuserbyid/';
@Injectable()
export class UserService {
  private readonly axiosInstance: AxiosInstance;

  constructor(
    @InjectModel('User') private readonly userModel: Model<UserDto>,
    private httpService: HttpService,
    private logService: LogService,
    private configService: ConfigService,
    @InjectModel('Message') private readonly messageModel:
      Model<MessageDto>, // private readonly authService: AuthService,
  ) {
    this.axiosInstance = axios.create({
      baseURL: this.configService.get('N_API'), // Replace with your API base URL
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
  async startChat(data: { user: JobleUserDto; senderId: string }) {
    try {
      this.logService.log('starting chat: ', data);

      const sender = await this.findOne(data.senderId);
      // this.logService.log('subdomain: ', sender.subdomain);
      let foundUser = await this.findByUserIdWithoutMsg(data.user.Id);
      if (!foundUser) {
        const userObj: CreateUserDto = {
          username: data.user.FirstName + ' ' + data.user.LastName,
          email: data.user.Email,
          subdomain: sender.subdomain,
          user_id: data.user.Id,
          phone: data.user.PhoneNo,
          companyId: data.user.CompanyId,
          tenantId: data.user.TenantId,
        };
        foundUser = await this.create(userObj);
      }
      // // this.logService.log('user and sender ', sender, foundUser);

      // this.logService.log('foundUser: ', foundUser.user_id, foundUser.username);
      // this.logService.log('sender: ', sender.user_id, sender.username);
      await this.connectUsers(data.senderId, foundUser.id);
      const senderConnections = await this.findMyConnection(data.senderId);
      const userConnections = await this.findMyConnection(foundUser.id)
      this.logService.log('sender connections ', senderConnections, ' receiver connections: ', userConnections);

      return { senderConnections, userConnections, foundUser }

    } catch (error) {
      this.logService.log('error starting chat: ', error)
    }
  }

  async updateColorPreference(data: {
    userId: string;
    colorPreference: string;
  }) {
    this.logService.log('user setting color: ', data);
    // const user = await this.userModel.findOne({ user_id: data.userId });
    // user.colorPreference = data.colorPreference;
    // const up = await this.userModel.updateMany({}, { $set: { colorPreference: data.colorPreference } });
    const up = await this.userModel.updateOne(
      { $or: [{ user_id: data.userId }, { _id: data.userId }] },
      { colorPreference: data.colorPreference },
    );
    this.logService.log('update res: ', up);

    if (up.modifiedCount) {
      return { message: 'color preference set successfully' };
      // return await this.userModel.findOne({ $or: [{ user_id: data.userId }, { _id: data.userId }] })
    }

    return { message: 'saving color preference failed' };
  }
  async deleteMessage(data) {
    const user = await this.userModel.findOne({ _id: data.myid });

    user.messages = user.messages.filter((id) => id != data.messageId);

    return await user.save();
  }
  async userExist(createUserDto: CreateUserDto): Promise<UserDto> {
    return await this.userModel.findOne({
      $or: [
        // { email: createUserDto.email },
        { username: createUserDto.username },
        { user_id: createUserDto.user_id },
      ],
    });
  }
  async create(createUserDto: CreateUserDto): Promise<UserDto> {
    try {

      this.logService.log('user to create: ', createUserDto);
      const exist = await this.userModel.findOne({ user_id: createUserDto.user_id });

      if (exist) {
        await this.userModel.updateOne({ user_id: createUserDto.user_id }, { ...createUserDto, email: createUserDto?.email.toLowerCase() })
        return await this.findOne(exist.id)
      }

      const user = new this.userModel(createUserDto);
      await user.save();
      // add user to the general circle of the company
      return user;
    } catch (error) {
      this.logService.error('user create error: ', error.message);
      return error;
    }
  }

  async findAll(tenantId?: string): Promise<UserDto[]> {
    if (tenantId && tenantId != null && tenantId != 'null') {
      this.logService.log('tenantId: ', tenantId);
      return await this.userModel.find({ tenantId });
    } else {
      this.logService.log('tenantId not provided');
      return await this.userModel.find();
    }
  }
  async findByUserIdWithoutMsg(user_id: string): Promise<UserDto> {
    this.logService.log('user_id..: ', user_id);
    if (!user_id) throw new BadRequestException('user id must be provided');
    const found = await this.userModel.findOne({ user_id }); //|| await this.userModel.findOne({ user_id });
    // this.logService.log(`user found: ${found}`); 7e0ded2f-f0b5-4531-b9e9-ad22417553f0
    return found;
  }
  async findByUserId(user_id: string): Promise<UserDto> {
    try {
      this.logService.log('user_id: ', user_id);
      if (!user_id) throw new BadRequestException('user id must be provided');
      const found = await this.userModel.findOne({ user_id }).populate([
        {
          path: 'connections',
        },
      ]);
      return found;
    } catch (error) {
      return error;
    }

  }
  async findOne(id: string): Promise<UserDto> {
    if (id != null && id != undefined && id != 'undefined') {
      this.logService.log('iddd: ', id);
      const user = (
        await this.userModel.findOne({
          $or: [{ _id: id.trim() }, { user_id: id?.trim() }],
        })
      )?.toJSON();
      return user;
    } else throw new BadRequestException('user id must be provided');
  }
  async findMyConnection(id: string): Promise<any> {
    try {
      if (id != null && id != undefined && id != 'undefined') {
        this.logService.log('my id: ', id);
        const user = await this.userModel.findOne({ _id: id }).populate([
          {
            path: 'connections',
            model: 'User', // populate connections,
            select: '-messages -rooms -connections',
          },
        ]);
        const formatted = [];
        for (let i = 0; i < user.connections.length; i++) {
          const unRead = await this.messageModel.countDocuments({ user: user.connections[i].id, receiverId: id, readBy: { $ne: id } });
          formatted.push({ ...user.connections[i]['_doc'], unRead, id: user.connections[i].id });
        }
        // this.logService.log('found connections: ', formatted);
        return formatted ?? [];
      }
    } catch (error) {
      this.logService.log('error: ', error);
    }
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    this.logService.log('updating user');
    const updatedUser = await this.userModel.findByIdAndUpdate(id, {
      updateUserDto,
    });
    return `This action updates a #${id} user ${updatedUser}`;
  }

  async delete(id: number) {
    return await this.userModel.findByIdAndRemove(id);
  }

  async mailExists(email: string): Promise<UserDto> {
    return await this.userModel.findOne({ email });
  }

  async usernameExists(username: string) {
    return await this.userModel.findOne({ username });
  }
  async connectUsers(senderId: string, userId: string) {
    this.logService.log('sender: ', senderId, 'userId: ', userId);

    // this.logService.log('sender: ', sender.connections, 'user: ', receiver.connections);
    if (senderId && userId) {
      await this.userModel.updateOne(
        { _id: senderId, connections: { $ne: userId } },
        {
          $push: { connections: userId },
        },
      );
      await this.userModel.updateOne(
        { _id: userId, connections: { $ne: senderId } },
        {
          $push: { connections: senderId },
        },
      );
    } else {
      this.logService.log('sender or receiver not fould');
    }
  }
}
