import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { fieldEncryption } from "mongoose-field-encryption";
import { FileDto } from "src/messages/dto/file.dto";

@Schema({
    timestamps: true,
    toObject: { virtuals: true },
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
})
export class Draft {
    @Prop({ type: Types.ObjectId, ref: 'Chat' })
    chatId: string;
    @Prop() text: string;
    @Prop() jobProUserId: string;
    @Prop() files: FileDto[]
}

export const DraftSchema = SchemaFactory.createForClass(Draft);
DraftSchema.plugin(fieldEncryption, {
    fields: ["text"],
    secret: process.env.MESSAGE_ENC_SECRET || '!325043439SKLEF232',
    encryptNull: false
})
