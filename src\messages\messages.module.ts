import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ActivityLogModule } from 'src/activity-log/activity-log.module';
import { FileModule } from 'src/file/file.module';
import { LogModule } from 'src/log/log.module';
import { MailModule } from 'src/mail/mail.module';
import { UserModule } from 'src/user/user.module';
import { UserSchema } from '../models/user.model';
import { ChatCallController } from './controllers/chat-call.controller';
import { ConnectionController } from './controllers/connection.controller';
import { MessageController } from './controllers/message.controller';
import { RoomController } from './controllers/room.controller';
import { CallSchema } from './models/chat_call.model';
import { ConnectedUserSchema } from './models/connected-user.model';
import { ConnectionSchema } from './models/connection.model';
import { MessageSchema } from './models/messages.model';
import { RoomSchema } from './models/room.model';
import { ChatCallService } from './service/chat-call.service';
import { ConnectedUserService } from './service/connected-user-service.service';
import { ConnectionService } from './service/connection.service';
import { MessagesGateway } from './service/messages.gateway';
import { MessagesService } from './service/messages.service';
import { RoomService } from './service/room.service';
import { Reaction, ReactionSchema } from './models/reaction.model';
import { ReactionService } from './service/reaction.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Message', schema: MessageSchema },
      { name: 'Room', schema: RoomSchema },
      { name: 'User', schema: UserSchema },
      { name: 'ConnectedUser', schema: ConnectedUserSchema },
      { name: 'Connection', schema: ConnectionSchema },
      { name: 'Call', schema: CallSchema },
      { name: Reaction.name, schema: ReactionSchema }
    ]),
    FileModule,
    UserModule,
    LogModule,
    MailModule,
    ActivityLogModule
  ],
  controllers: [MessageController, RoomController, ConnectionController, ChatCallController],
  providers: [
    ConnectionService,
    MessagesGateway,
    MessagesService,
    RoomService,
    ConnectedUserService,
    ChatCallService,
    ReactionService
  ],
  exports: [ConnectedUserService]
})
export class MessagesModule { }
