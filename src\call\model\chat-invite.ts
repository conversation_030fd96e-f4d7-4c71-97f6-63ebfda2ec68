import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";

@Schema({
    timestamps: true,
    virtuals: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class ChatInvite {
    @Prop() chatId: string;
    @Prop() chatName: string;
    @Prop() senderJobProUserId: string;
    @Prop() jobProUserId: string;
    @Prop() createdAt: Date;
    @Prop() updatedAt: Date;
    @Prop() type: string;
}

export const ChatInviteSchema = SchemaFactory.createForClass(ChatInvite);