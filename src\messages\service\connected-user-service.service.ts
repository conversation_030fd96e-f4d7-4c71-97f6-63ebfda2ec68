import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Schema, Types } from 'mongoose';
import { User } from 'src/models/user.model';
import { ConnectedUserDto } from '../dto/connected-user.dto';
import { LogService } from 'src/log/log.service';

@Injectable()
export class ConnectedUserService {
  constructor(
    @InjectModel('ConnectedUser')
    private readonly connectedUserModel: Model<ConnectedUserDto>,
    @InjectModel('User') private readonly userModel: Model<User>,
    private logService: LogService
  ) { }

  async create(
    socketId: string,
    userId: Types.ObjectId | string,
  ): Promise<ConnectedUserDto> {
    try {
      this.logService.log('data to save: ', socketId, userId);
      const found = await this.connectedUserModel.findOne({
        socketId,
        // user: new Types.ObjectId(userId),
      });
      if (!found) {
        const newConnectedUser = new this.connectedUserModel({
          socketId,
          user: userId,
        });
        const saved = await newConnectedUser.save();
        this.logService.log('saved connection: ', saved);
        // await this.addConnectionToUser(newConnectedUser._id, userId);
        return newConnectedUser;
      }
      return found;
    } catch (error) {
      this.logService.log('error in connected user: ', error.message);
    }
  }

  async findByUser(userId: string): Promise<ConnectedUserDto[]> {
    return await this.connectedUserModel
      .find({ user: userId })
      .populate('user');
  }

  async deleteBySocketId(socketId: string) {
    const connectedUser = await this.connectedUserModel.findOneAndDelete({
      socketId,
    });
    if (!connectedUser) {
      this.logService.log('User is not connected');
      return;
    }
    // await this.removeConnectionFromUser(
    //   connectedUser.id,
    //   connectedUser.user['_id'],
    // );
    return { msg: 'success, connection removed' };
  }

  async deleteAll() {
    this.logService.warn('deleting all');
    await this.connectedUserModel.deleteMany();
    // await this.userModel.updateMany({}, { connections: [] });
    return;
  }

  private async addConnectionToUser(
    connectionId: Schema.Types.ObjectId,
    userId: Schema.Types.ObjectId,
  ) {
    return await this.userModel.findByIdAndUpdate(
      userId,
      { $push: { connections: connectionId } },
      { new: true, useFindAndModify: false },
    );
  }

  private async removeConnectionFromUser(
    connectionId: Schema.Types.ObjectId,
    userId: Schema.Types.ObjectId,
  ) {
    return await this.userModel.findByIdAndUpdate(
      userId,
      { $pull: { connections: connectionId } },
      { new: true, useFindAndModify: false },
    );
  }
}
