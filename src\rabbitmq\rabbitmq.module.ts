import { LogModule } from 'src/log/log.module';
import { RabbitmqController } from './rabbitmq.controller';
import { RabbitMQService } from './rabbitmq.service';
/*
https://docs.nestjs.com/modules
*/

import { Global, Module } from '@nestjs/common';

import { ClientsModule, Transport } from '@nestjs/microservices'
import { HttpModule } from '@nestjs/axios';

// Set how long the message should live on the queue i.e ttl
const expiration = 360000000; // 100 hours
const queueArgs = {
    'x-message-ttl': expiration
};

@Global()
@Module({
    imports: [LogModule, HttpModule, ClientsModule.register([
        {
            name: 'USER_SERVICE',
            transport: Transport.RMQ,
            options: {
                urls: [`amqps://xruwodqv:<EMAIL>/xruwodqv`],
                queue: 'user-created-queue',
                queueOptions: {
                    durable: true,
                    arguments: queueArgs
                },
            },
        },
        {
            name: 'NOTIFICATION_SERVICE',
            transport: Transport.RMQ,
            options: {
                urls: [`amqps://xruwodqv:<EMAIL>/xruwodqv`],
                queue: 'notification-message-queue',
                queueOptions: {
                    durable: true,
                    arguments: queueArgs
                },
            },
        },
        {
            name: 'HTTP_MESSAGE_SERVICE',
            transport: Transport.RMQ,
            options: {
                urls: [`amqps://xruwodqv:<EMAIL>/xruwodqv`],
                queue: 'http-message',
                queueOptions: {
                    durable: true
                    // No TTL arguments for http-message queue
                },
            },
        },
    ]),],
    controllers: [
        RabbitmqController,],
    providers: [
        RabbitMQService,
    ],
    exports: [RabbitMQService]
})
export class RabbitmqModule { }
