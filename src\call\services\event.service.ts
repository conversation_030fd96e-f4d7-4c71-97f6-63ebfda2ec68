/*
https://docs.nestjs.com/providers#services
*/

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { Model } from 'mongoose';
import { AnnouncementDto } from '../dto/announcement.dta';
import { EventParticipantDto } from '../dto/event_participant.dto';
import { ScheduleEventDto } from '../dto/eventschedule.dto';
import { UserAvailabilityDto } from '../dto/user-availability.dto';
import { AnnouncementSchema } from '../model/announcement.model';
import { LogService } from 'src/log/log.service';
import { InterestArea } from '../model/interestarea';
import { InterestAreaDto } from '../dto/interest_area.dto';

@Injectable()
export class EventService {


    constructor(
        @InjectModel(InterestArea.name) private interestAreaModel: Model<InterestAreaDto>,
        @InjectModel('Announcement') private readonly announcementModel: Model<AnnouncementDto>,
        @InjectModel('ScheduleEvent') private readonly scheduleModel: Model<ScheduleEventDto>,
        @InjectModel('EventParticipant') private readonly eventParticipantModel: Model<EventParticipantDto>,
        @InjectModel('UserAvailability') private readonly availabilityModel: Model<UserAvailabilityDto>,
        private logService: LogService,) { }

    async createAnnouncement(data: AnnouncementDto) {
        try {
            return await this.announcementModel.create(data);
        } catch (error) {

        }
    }
    async getAllAnnouncements(eventId: string) {
        try {
            if (!eventId) {
                throw new BadRequestException('event id is required to get announcement');
            }
            this.logService.log('eventId: ', eventId);
            return await this.announcementModel.find({ eventId })
        } catch (error) {

        }
    }

    async deleteAnnouncements(): Promise<any> {
        return await this.announcementModel.deleteMany({ eventEndDate: { $lt: new Date() } });
    }
    async getParticipantByUserId(receiverId: string) {
        try {
            return await this.eventParticipantModel.findOne({ userId: receiverId });
        } catch (error) {
            console.log('error getting participants: ', error);
        }
    }


    async removeParticipantBySocketId(socketId: string) {
        try {
            const found = await this.eventParticipantModel.findOne({ socketId });
            if (found) {
                const del = this.eventParticipantModel.updateOne({ _id: found.id }, { is_online: false })
                console.log('found: ', found['userId']);
                return found;
                // console.log('del: ', del);
            }
            return null;
        } catch (error) {

        }
    }
    async registerInterestArea(interesArea: InterestAreaDto) {
        console.log('interest area to register: ', interesArea)
        const found = await this.interestAreaModel.findOne({ userId: interesArea.userId, eventId: interesArea.eventId });
        if (found) {
            found.interests = interesArea.interests;
            return await found.save();
        }
        else {
            const toSave = await this.interestAreaModel.create({ name: interesArea.name, eventId: interesArea.eventId, userId: interesArea.userId, interests: interesArea.interests });
            return toSave.save();
        }
    }
    async registerParticipant(data: EventParticipantDto): Promise<any> {
        try {
            //  { userId?: string; eventId?: string; interests?: string[]; media?: string, eventStartDate?: Date, eventEndDate?: Date, socketId?: string }
            console.log('participant to add : ', data);
            if (!data.userId || !data.eventId) {
                console.log("no user or no eventId")
                throw new BadRequestException('user id and event id are required');
            }
            const found = await this.eventParticipantModel.findOne({ eventId: data.eventId, userId: data.userId });
            console.log('found user: ', found);
            if (found) {
                // update
                const up = await this.eventParticipantModel.updateOne({ eventId: data.eventId, userId: data.userId }, { ...data, is_online: true });
                console.log('update: ', up);
                const res = await this.eventParticipantModel.findOne({ _id: found.id });
                // console.log('update user res: ', res);

            } else {
                delete data['id'];
                const saved = await this.eventParticipantModel.create({ ...data, is_online: true, name: data.name ? data.name : 'Guest' });
                console.log('saved participant: ', saved);

            }
            return this.eventParticipantModel.find({ eventId: data.eventId });
        } catch (error: any) {
            console.log('error register parts: ', error);
        }
    }
    async updateSchedule(id: string): Promise<any> {

        try {
            console.log('schedule id: ', id)
            const found = await this.scheduleModel.findOne({ _id: id });
            // console.log('found schedule:', found);
            if (found) return await this.scheduleModel.updateOne({ _id: id }, { status: 'ended' });


        } catch (error) {
            console.log('error in update schedule: ', error.message)
        }
    }

    async getMyInterestArea(userId: string, eventId: string) {
        try {
            return await this.interestAreaModel.findOne({ userId, eventId });
        } catch (error) {

        }
    }

    async getAllParticipants(eventId: string) {
        try {
            console.log('getting all participants: ', eventId)
            const all = await this.eventParticipantModel.find({ eventId, userId: { $ne: 'null' }, is_online: true });
            // console.log('getall:', all)
            const formatted = all.map(participant => { if (Array.isArray(participant) == false) { return participant } });
            // console.log('formatted: ', formatted);
            return formatted;
        } catch (error) {
        }
    }

    async scheduleMeeting(data: ScheduleEventDto) {
        try {
            const schedule = await this.scheduleModel.create(data);
            console.log('event schedule: ', schedule);
            return schedule;
        } catch (error) {
            console.error('error booking meeting: ', error)
        }
    }
    async updateInvite(data: ScheduleEventDto): Promise<any> {
        try {
            return await this.scheduleModel.updateOne({ _id: data['_id'] }, data);
        } catch (error) {

        }
    }

    async getMyInvites(ticketId: string) {
        try {
            const invites = await this.scheduleModel.find({
                $or: [
                    { userId: ticketId },    // my sent invites
                    { participantId: ticketId } // my received invites
                ]
            })
            return invites;
        } catch (error) {

        }
    }

    async createAvailability(data: UserAvailabilityDto): Promise<any> {
        try {
            console.log('data to save: ', data);
            try {
                if (!data.userId || !data.eventId) {
                    throw new BadRequestException('user id and event id is required');
                }
                const found = await this.availabilityModel.findOne({ userId: data.userId, eventId: data.eventId })

                if (found) {
                    return await this.availabilityModel.updateOne({ _id: found.id }, data);
                }
                return await this.availabilityModel.create(data);
            } catch (error: any) {
                console.log('Error: ', error);
                throw error;
            }
        } catch (error) {

        }
    }
    async getAvailability(userId: string, eventId: string) {
        try {
            console.log('userId: ', userId, ' eventId: ', eventId);
            if (!userId || !eventId) {
                throw new BadRequestException('user id and event id are required')
            }
            const found = await this.availabilityModel.findOne({ userId, eventId })
            // console.log('availability: ', found);
            return found;
        } catch (error) {

        }
    }

    async getSchedules() {
        try {
            const schedules = await this.scheduleModel.find({ status: 'accepted' });
            return schedules;

            // console.log('schedules: ', schedules);
        } catch (error) {

        }

    }
    async updateMatch(data: any): Promise<any> {
        try {
            // console.log('update match data: ', data);
            const updatesender = await this.eventParticipantModel.updateOne({ eventId: data.eventId, userId: data.senderId }, { $push: { matches: data.sender['_id'] } });
            const updateRec = await this.eventParticipantModel.updateOne({ _id: data['_id'] }, { $push: { matches: data['_id'] } });
            // console.log('updated match', updatesender, updateRec);
            return { updatesender, updateRec }
        } catch (error) {
            console.log('update match error: ', error.message);
            return error;
        }
    }
    @Cron(CronExpression.EVERY_DAY_AT_1PM)
    async deleteOldParticipants() {
        try {
            //    const allPast = await this.eventParticipantModel.find({ eventEndDate: { $lt: new Date() } });
            //   const pastEvents= allPast.map(p =>p.eventId);
            //   this.interestAreaModel.deleteMany({eventId:{$in:pastEvents}})
            await this.eventParticipantModel.deleteMany({ eventEndDate: { $lt: new Date() } })

        } catch (error) {
            console.log('error deleting old participants: ', error?.message)
        }
    }
    async matchUser(senderName: string, userId: string, eventId: string, interests: string[]) {
        try {
            const matchingUsers = await this.interestAreaModel.find({ userId: { $ne: userId }, eventId: eventId, interests: { $in: interests } });

            const res = this.selectRandomUser(matchingUsers, interests, userId).map(user => {
                return {
                    ...user['_doc'], matchPercentage:
                        parseFloat(user.matchPercentage).toFixed(2),
                    senderName,
                    commonInterests: user.commonInterests
                }
            }).sort((a, b) => b.matchPercentage - a.matchPercentage);
            console.log('matched: ', res);
            return res
            // console.log('random user: ', randomUser);
            // return { ...randomUser['_doc'], senderName, senderId: userId, eventId, matchPercentage: parseFloat(randomUser['matchPercentage']).toFixed(2) };
        } catch (error) {
            console.log('error: ', error)
        }
    }
    selectRandomUser = (matchingUsers: any[], interests: string[], currentUserId: string) => {
        try {
            // Exclude the current user from the matching users
            const filteredUsers = matchingUsers.filter((user) => {
                return user._id !== currentUserId && !user.matches?.includes(currentUserId);
            });

            if (filteredUsers.length === 0) {
                // Handle case when no eligible users are found
                return null;
            }
            // Calculate match percentages and assign weights
            const weightedMatchingUsers = filteredUsers.map((user) => ({
                ...user,
                ...this.calculateMatchPercentage(user.interests, interests),
            }));
            return weightedMatchingUsers;
        } catch (error) {

        }
    };

    getRandomUser = (matchingUsers: any[], weights: number[]) => {
        try {
            const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
            let randomNum = Math.random() * totalWeight;
            let selectedUser;

            for (let i = 0; i < matchingUsers.length; i++) {
                randomNum -= weights[i];
                if (randomNum <= 0) {
                    selectedUser = matchingUsers[i];
                    break;
                }
            }
            return selectedUser;
        } catch (error) {

        }
    };

    assignWeights = (matchingUsers: any[]) => {
        const weights = matchingUsers.map((user) => user.matchPercentage);
        return weights;
    };

    calculateMatchPercentage = (userInterests: string[], interests: string[]) => {
        console.log('userInterests: ', userInterests, ' interests: ', interests)
        const commonInterests = userInterests.filter((interest) => interests.includes(interest));
        const matchPercentage = (commonInterests.length / interests.length) * 100;
        return { matchPercentage, commonInterests };
    };
}