/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable prettier/prettier */
/*
https://docs.nestjs.com/providers#services
*/

import { Storage } from '@google-cloud/storage';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { randomBytes, } from 'crypto';
import * as path from 'path';

import ffmpeg from 'fluent-ffmpeg';
import { PassThrough } from 'stream';
import { Response } from 'express';
import { FileDto } from 'src/messages/dto/file.dto';
import { LogService } from 'src/log/log.service';
import { AwsService } from './awsservice';
// const random = (() => {
//     const buf = Buffer.alloc(16);
//     return () => randomFillSync(buf).toString('hex');
// })();

@Injectable()
export class FileService {

    constructor(
        private configService: ConfigService,
        private logService: LogService,
        private awsService: AwsService
    ) {
    }

    async getGCSFilePublicUrl(filename: string) {
        const storage = new Storage({
            keyFilename: path.join(__dirname, '..', '..', 'service-account-key.json')
        })

        const bucketName = this.configService.get('GCS_BUCKET_NAME');
        const bucket = storage.bucket(bucketName);
        const file = bucket.file(filename);

        const today = new Date();
        const expiresIn = today.setDate(today.getFullYear() + 1);

        const publicUrl = await file.getSignedUrl({
            action: 'read',
            expires: expiresIn
        });
        // this.logService.log('public url: ', publicUrl);
        return publicUrl[0]
    }

    getFromGCS(filename: string, res: Response) {
        try {
            this.logService.log('file name: ', filename);
            const storage = new Storage({
                keyFilename: path.join(__dirname, '..', '..', 'service-account-key.json')
            })

            const bucketName = this.configService.get('GCS_BUCKET_NAME');
            const bucket = storage.bucket(bucketName);
            const file = bucket.file(filename);

            const fileStream = file.createReadStream();

            // Set appropriate headers for the response
            res.setHeader('Content-disposition', `attachment; filename=${filename}`);
            res.setHeader('Content-type', 'application/octet-stream');

            fileStream.pipe(res);
        } catch (error) {
            this.logService.error(error);
            res.status(500).send('Internal Server Error');
        }
    }

    async gcsUploadWithResumeable(file: Express.Multer.File) {
        try {

            const fileName = file.originalname;
            const contentType = file.mimetype;
            const fileContent = file.buffer;

            const randomName = `${Date.now()}-${randomBytes(16).toString('hex')}`;

            const fileExtension = path.extname(file.originalname);

            const objectName = `${randomName}${fileExtension}`;

            const storage = new Storage({
                keyFilename: path.join(__dirname, '..', '..', 'service-account-key.json')
            })

            const bucketName = this.configService.get('GCS_BUCKET_NAME');
            const bucket = storage.bucket(bucketName);

        } catch (err) {
            this.logService.error('Error handling upload:', err);
            // res.status(500).send({ error: 'Internal server error!' });
            throw err;
        }
    }

    async gcsUpload(file: Express.Multer.File) {
        try {
            // Generate a unique name for the file
            const randomName = `${Date.now()}-${randomBytes(16).toString('hex')}`;
            const isAudioFile = file.mimetype.startsWith('audio/');
            const fileExtension = isAudioFile ? '.wav' : path.extname(file.originalname);
            const objectName = `${randomName}${fileExtension}`;

            const storage = new Storage({
                keyFilename: path.join(__dirname, '..', '..', 'service-account-key.json')
            });

            const bucketName = this.configService.get('GCS_BUCKET_NAME');
            const bucket = storage.bucket(bucketName);

            // Create a PassThrough stream to pipe data to GCS after conversion (if needed)
            const fileStream = bucket.file(objectName).createWriteStream({
                metadata: {
                    contentType: isAudioFile ? 'audio/wav' : file.mimetype,
                },
            });

            // Handle file buffer based on file type
            if (isAudioFile) {
                // Convert audio to WAV using ffmpeg and pipe output to GCS write stream
                const bufferStream = new PassThrough();
                bufferStream.end(file.buffer);

                await new Promise((resolve, reject) => {
                    ffmpeg(bufferStream)
                        .toFormat('wav')
                        .pipe(fileStream)
                        .on('finish', resolve)
                        .on('error', reject);
                });
            } else {
                // Directly pipe non-audio files to GCS without conversion
                fileStream.end(file.buffer);
                await new Promise((resolve, reject) => {
                    fileStream.on('finish', resolve);
                    fileStream.on('error', reject);
                });
            }

            // Get the metadata of the uploaded file
            const [metadata] = await bucket.file(objectName).getMetadata();

            // Generate a signed URL with an expiration of one year
            const today = new Date();
            const expiresIn = today.setFullYear(today.getFullYear() + 1);
            const publicUrl = await bucket.file(objectName).getSignedUrl({
                action: 'read',
                expires: expiresIn
            });

            // Return the file information
            const fileInfo = {
                filename: objectName,
                size: metadata.size,
                type: metadata.contentType,
                url: publicUrl[0],
                originalname: file.originalname,
            };

            this.logService.log('file saved: ', fileInfo);
            return fileInfo;

        } catch (error) {
            this.logService.error('Error uploading file:', error);
            throw new Error('Failed to upload file');
        }
    }

    // AWS S3 Methods
    async getS3FilePublicUrl(filename: string) {
        return await this.awsService.getS3FilePublicUrl(filename);
    }

    getFromS3(filename: string, res: Response) {
        return this.awsService.getFromS3(filename, res);
    }

    async s3UploadWithResumeable(file: Express.Multer.File) {
        // This method can be implemented later for resumable uploads if needed
        // For now, delegate to the standard upload method
        return await this.s3Upload(file);
    }

    async s3Upload(file: Express.Multer.File) {
        return await this.awsService.s3Upload(file);
    }

    /**
     * Delete a file from S3
     * @param filename - The filename/key in S3
     * @returns Promise<boolean> - Success status
     */
    async deleteFromS3(filename: string): Promise<boolean> {
        return await this.awsService.deleteFromS3(filename);
    }

    /**
     * Check S3 connection status
     * @returns Promise<boolean> - Connection status
     */
    async checkS3Connection(): Promise<boolean> {
        return await this.awsService.checkS3Connection();
    }
}
