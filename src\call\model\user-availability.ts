// import mongoose from 'mongoose';

import { Prop, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";


// export const UserAvailabilitySchema = new mongoose.Schema<IUserAvailability>({
//     userId: { type: String, required: true },
//     eventId: { type: String, required: true },
//     timeZone: { type: String },
//     days: [
//         {
//             weekday: { type: String, required: true },
//             date: { type: String, required: true },
//             fullDate: { type: Date, },
//             month: { type: Number },
//             availabilities: [
//                 {
//                     startTime: { type: String, required: true },
//                     endTime: { type: String, required: true },
//                     duration: { hours: Number, minutes: Number },
//                 },
//             ],
//         },
//     ],
// });

// export interface IUserAvailability extends Document {
//     userId: string;
//     eventId: string;
//     timeZone: string;
//     days: [
//         {
//             weekday: string;
//             date: string;
//             fullDate: { type: Date, },
//             month: { type: Number },
//             availabilities: [
//                 {
//                     startTime: string;
//                     endTime: string;
//                     duration: { hours: number; minutes: number };
//                 },
//             ];
//         },
//     ];
// }


@Schema()
export class UserAvailability {
    @Prop({ required: true }) userId: string;
    @Prop({ required: true }) eventId: string;
    @Prop() timeZone: string;
    @Prop() days: [
        {
            weekday: { type: String, required: true },
            date: { type: String, required: true },
            fullDate: { type: Date, },
            month: { type: Number },
            availabilities: [
                {
                    startTime: { type: String, required: true },
                    endTime: { type: String, required: true },
                    duration: { hours: Number, minutes: Number },
                },
            ],
        },
    ];
}
export const UserAvailabilitySchema = SchemaFactory.createForClass(UserAvailability)

