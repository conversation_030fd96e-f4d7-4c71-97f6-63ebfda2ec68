import { Is<PERSON><PERSON><PERSON>, IsO<PERSON>, IsString, IsBoolean } from "class-validator";
import { UserDto } from "src/user/dto/user.dto";
import { FileDto } from "./file.dto";
import { MESSAGE_TYPE } from "./message_type.enum";

export class FileMessageDto {
    @IsArray() files: FileDto

    @IsObject()
    user: UserDto;

    @IsString() receiverId: string;
    @IsString() room: string;
    @IsBoolean() is_private: boolean;
    messageType: MESSAGE_TYPE
}