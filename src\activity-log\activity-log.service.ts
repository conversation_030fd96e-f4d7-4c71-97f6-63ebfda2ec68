/*
https://docs.nestjs.com/providers#services
*/

import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { ActivityLogDto } from './activity-log.dto';
import { ConfigService } from '@nestjs/config';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { LogService } from 'src/log/log.service';

export const logEndpoint = 'Activity/CreateLog';

@Injectable()
export class ActivityLogService {
    private readonly axiosInstance: AxiosInstance;

    constructor(
        private httpService: HttpService,
        private userService: ChatuserService,
        private logService: LogService,
        private configService: ConfigService,
    ) {
        this.axiosInstance = axios.create({
            baseURL: this.configService.get('JOBLE_API'), // Replace with your API base URL
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }

    async logActivity(data: ActivityLogDto) {
        try {
            this.logService.log('data to log: ', data);
            const user = await this.userService.findUser(data.UserId);
            this.logService.log('user subdomain: ', user);
            // this.axiosInstance.defaults.headers['subdomain'] = user?;
            // data = { ...data, UserId: user.user_id };
            // this.logService.log('data to log: ', data);
            // const response = await this.axiosInstance.post(this.configService.get('PACTOCOIN_API') + logEndpoint,
            //     { ...data, UserId: user.user_id });
            // return response.data;

        } catch (error) {
            this.logService.log('Activity Log Error: ', error.message);
        }

    }
}
