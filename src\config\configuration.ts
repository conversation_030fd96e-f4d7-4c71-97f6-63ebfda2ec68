/* eslint-disable prettier/prettier */
export default () => ({
    PORT: parseInt(process.env.PORT) || 3000,
    AWS_ACCESS_KEY: process.env.AWS_ACCESS_KEY || '********************',
    AWS_SECRET_SECRET_KEY: process.env.AWS_SECRET_SECRET_KEY || 'tZAcuySML6fTFiTBddv/VpRPUPHqqdAbWQvFFEfr',
    AWS_REGION: process.env.AWS_REGION || 'eu-central-1',
    AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME || 'jobpro-public-dev-eu-central-1-010526262538',
    GCS_BUCKET_NAME: process.env.GCS_BUCKET_NAME || 'jobchat',
    MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/chatserviceDB',
    //'mongodb+srv://test-user:<EMAIL>/stagingDB?retryWrites=true&w=majority&appName=Chat-service'
    MONGODB_URI1: 'mongodb+srv://test_user:<EMAIL>/migratedDB?retryWrites=true&w=majority&appName=Chat-service',
    LIVE_KIT_KEY: process.env.LIVE_KIT_KEY || '',
    LIVE_KIT_SECRET: process.env.LIVE_KIT_SECRET || '',
    NODE_ENV: process.env.NODE_ENV || 'development',
    PACTOCOIN_API: process.env.PACTOCOIN_API || '',
    OPEN_API_KEY: process.env.OPEN_API_KEY || '',
    MESSAGE_ENC_SECRET: process.env.MESSAGE_ENC_SECRET || '!',
    MAILGUN_KEY: process.env.MAILGUN_KEY || '**************************************************',
    MAILGUN_DOMAIN: process.env.MAILGUN_DOMAIN || 'jobpro.app',
    CLIENT_BASE_URL: process.env.CLIENT_BASE_URL || '',
    AUTH_SCREET: process.env.JOBPRO_JWT_SHARED_SECRET || '',
    JOBLE_API: process.env.JOBLE_API || process.env.PACTOCOIN_API || 'https://api.pactocoin.com/notify/api/',
    REDIS_SERVER: process.env.REDIS_SERVER || 'redis://redis-11854.c239.us-east-1-2.ec2.redns.redis-cloud.com:11854',
    REDIS_PASSWORD: process.env.REDIS_PASSWORD || 'hgIRV9RTvmy8qcKVo5t4G6QKPVX8xsjs',

   // mongodb+srv://kingstanley:<EMAIL>/chatserviceDB?retryWrites=true&w=majority
});   