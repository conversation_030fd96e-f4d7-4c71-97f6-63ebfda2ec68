import { BadRequestException, Logger, OnModuleInit, UnauthorizedException } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import {
  ActivityLogDto,
  EventCategory,
} from 'src/activity-log/activity-log.dto';
import { ActivityLogService } from 'src/activity-log/activity-log.service';
import { UserDto } from 'src/user/dto/user.dto';
import { UserService } from 'src/user/services/v1/user.service';
import {
  CHAT_CALL_STATUS,
  CHAT_CALL_TYPE,
  ChatCallDto,
} from '../dto/chat-call.dto';
import { CreateMessageDto } from '../dto/create-message.dto';
import { MessageDto } from '../dto/message.dto';
import { MESSAGE_TYPE } from '../dto/message_type.enum';
import { RoomDto } from '../dto/room.dto';
import { ConnectedUserService } from './connected-user-service.service';
import { LogService } from 'src/log/log.service';
import { v4 } from 'uuid';
import { AddUserToRoomDto } from '../dto/add_users_to_room.dto';
import { EVENT_NAME, EventPayloadDto, MESSAGE_EVENT } from '../dto/event.payload.dto';
import { ChatCallService } from './chat-call.service';
import { ConnectionService } from './connection.service';
import { MessagesService } from './messages.service';
import { RoomService } from './room.service';
import { ReactionDto } from '../dto/reaction.dto';
import { ReactionService } from './reaction.service';
import { MailgunService } from 'src/mail/services/mailgun.service';
import { WSAuthGuard } from 'src/user/ws.auth.guard';
import { GetPrivateMessageByPageDto } from '../dto/get_private_message.dto';
import { UseGuards } from '@nestjs/common';

@WebSocketGateway({

  cors: {
    origin: '*',
  },
})

export class MessagesGateway
  implements OnGatewayConnection, OnGatewayDisconnect, OnModuleInit {
  @WebSocketServer()
  server: Server;
  users = new Map<string, Socket[]>();
  constructor(
    private readonly messagesService: MessagesService,
    // private readonly logService: LogService,
    private readonly userService: UserService,
    private readonly roomService: RoomService,
    private readonly connectedUserService: ConnectedUserService,
    private readonly messageService: MessagesService,
    private readonly connectionRequestService: ConnectionService,
    private logService: LogService,
    private activityLogService: ActivityLogService,
    private readonly chatCallService: ChatCallService,
    private reactionService: ReactionService,
    private mailgunService: MailgunService
  ) { }
  async onModuleInit() {
    await this.connectedUserService.deleteAll();
  }

  async handleConnection(@ConnectedSocket() socket: Socket) {
    try {
      console.log('connection: ', socket.id);
      // this.server.to(socket.id).emit('send-data', 'send user data');
      const query = socket.handshake.query;
      console.log('query: ', query, socket.id);
      if (query.userId && query.userId != 'undefined' && query.tenantId && query.tenantId != 'undefined') {
        console.log('query has user id and tenantId')
        const userId = query.userId.toString();

        this.checkUserSocket(socket, userId)
        this.logService.log('users map: ', this.users, userId,);
        // this.getMyChannels(socket, { id: userId, subdomain: tenantId })
      }
    } catch (error) {
      this.server.send('error', error);
      return this.disconnect(socket);
    }
  }

  async handleDisconnect(@ConnectedSocket() socket: Socket) {
    console.log('disconnecting socket id: ', socket.id);
    await this.connectedUserService.deleteBySocketId(socket.id);
    socket.disconnect();

    for (const [key, values] of this.users.entries()) {

      const index = values.indexOf(socket);
      if (index !== -1) {
        values.splice(index, 1);

        if (values.length === 0) {
          this.users.delete(key);
        }
      }
    }
  }

  private async disconnect(@ConnectedSocket() socket: Socket) {
    socket.emit('error', new UnauthorizedException());
    await this.connectedUserService.deleteBySocketId(socket.id);
    socket.disconnect();
  }
  @UseGuards(WSAuthGuard)
  @SubscribeMessage(EVENT_NAME)
  handleMessage(@ConnectedSocket() socket: Socket, @MessageBody() payload: EventPayloadDto) {

    console.log('new event: ', payload);
    if (payload && payload?.event && payload.data) {
      switch (payload.event) {
        case MESSAGE_EVENT.CREATE_CIRCLE:
          this.onCreateRoom(socket, payload.data);
          break;
        case MESSAGE_EVENT.ADD_USERS_TO_CIRCLE:
          this.addUsersToChannel(payload.data, socket);
          break;
        case MESSAGE_EVENT.ADD_USER_TO_CIRCLE:
          this.addUserToChannel(payload.data, socket);
          break;
        case MESSAGE_EVENT.CHAT_CALL:
          this.chatCall(socket, payload.data);
          break;
        case MESSAGE_EVENT.CHAT_CALL_RESPONSE:
          this.chatCallResponse(socket, payload.data);
          break;
        case MESSAGE_EVENT.DELETE_MESSAGE_ALL:
          this.deleteMessageAll(payload.data);
          break;
        case MESSAGE_EVENT.DELETE_MESSAGE_ME:
          this.deleteMessageMe(payload.data, socket);
          break;
        case MESSAGE_EVENT.GET_ALL_UNREAD_MESSAGES:
          this.getAllUnReadMessages(socket, payload.data);
          break;
        case MESSAGE_EVENT.GET_PRIVATE_MESSAGES:
          // console.log('private msg payload: ', payload)
          this.getPrivateChats(socket, payload.data);
          break;

        case MESSAGE_EVENT.GET_PRIVATE_MESSAGES_BY_PAGE:
          // console.log('private msg payload: ', payload)
          this.getPrivateChatsByPage(socket, payload.data);
          break;

        case MESSAGE_EVENT.JOIN_CIRCLE:
          this.onJoinRoom(socket, payload.data);
          break;

        case MESSAGE_EVENT.NEW_MESSAGE:
          this.onNewMessage(socket, payload.data);
          break;
        case MESSAGE_EVENT.NEW_USER:
          this.createNewUser(payload.data, socket);
          break;
        case MESSAGE_EVENT.REQUEST_CHANNELS:
          this.getMyChannels(socket, payload.data);
          break;
        case MESSAGE_EVENT.TYPING:
          this.typing(payload.data, socket);
          break;
        case MESSAGE_EVENT.UPDATE_READ_STATUS:
          this.updateReadStatus(socket, payload.data);
          break;
        case MESSAGE_EVENT.USER_DATA:
          // this.onUserData(socket, payload.data);
          break;
        case MESSAGE_EVENT.ADD_REACTION:
          this.addReactionToMessage(socket, payload.data)
          break;
        case MESSAGE_EVENT.ADD_CONNECTION:
          this.addConnection(socket, payload.data)
          break;
        case MESSAGE_EVENT.REQUEST_CONNECTIONS:
          this.handleRequestConnections(socket, payload.data);
        default:
          break;
      }
    }
  }
  async handleRequestConnections(socket: Socket, data: { id: string, subdomain: string }) {
    try {
      if (!data.id || !data.subdomain) {
        throw new BadRequestException('user id and sub domain are required to request connection')
      }
      const myConnections = await this.userService.findMyConnection(data.id);
      const payload = new EventPayloadDto();
      payload.data = myConnections;
      payload.event = MESSAGE_EVENT.REQUEST_CONNECTIONS;

      socket.emit(EVENT_NAME, payload)
    } catch (error) {
      console.log('Error requesting connection: ', error);
      socket._error(error);
    }
  }
  async addConnection(socket: Socket, data: any) {
    try {
      console.log('adding connection: ', data);
      const { senderConnections, userConnections, foundUser } = await this.userService.startChat(data);
      const senderSocketIds = this.getUserSocketIds(data.senderId);
      const receiverSocketIds = this.getUserSocketIds(foundUser.id);
      console.log(' senderSocketIds: ', senderSocketIds);
      console.log(' receiverSocketIds: ', receiverSocketIds);
      const senderPayload: EventPayloadDto = {
        event: MESSAGE_EVENT.ADD_CONNECTION,
        data: senderConnections
      }
      const receiverPayload: EventPayloadDto = {
        event: MESSAGE_EVENT.ADD_CONNECTION,
        data: userConnections
      }
      this.server.to(senderSocketIds).emit(EVENT_NAME, senderPayload);
      if (receiverSocketIds.length) {
        this.server.to(receiverSocketIds).emit(EVENT_NAME, receiverPayload)
      }
    } catch (error) {
      this.logService.log('add connection error: ', error)
    }
  }
  async addReactionToMessage(socket: Socket, data: ReactionDto) {
    const senderSockets = this.getUserSocketIds(data.user);

    const reaction = await this.reactionService.createReaction(data);
    // this.logService.log('reaction update: ', message, data);
    const payload: EventPayloadDto = { event: MESSAGE_EVENT.ADD_REACTION, data: reaction }

    if (data.receiver) {
      console.log('emiting reaction for private:', payload, socket.rooms)
      const receiverSockets = this.getUserSocketIds(data.receiver);
      this.server.to([...senderSockets, ...receiverSockets]).emit(EVENT_NAME, payload);
    } else {
      console.log('emitting reaction:', payload, socket.rooms)
      this.server.to(data.room).emit(EVENT_NAME, payload);

    }
    this.messageService.addReaction(reaction.id, data.messageId);

  }

  async removeReactionFromMessage(socket, data: ReactionDto) {
    const senderSockets = this.getUserSocketIds(data.user);
    if (data.receiver) {
      const receiverSockets = this.getUserSocketIds(data.receiver);
      this.server.to([...senderSockets, ...receiverSockets]).emit(MESSAGE_EVENT.REMOVE_REACTION, data);
    } else {
      this.server.to(data.room).emit(MESSAGE_EVENT.ADD_REACTION, data);
    }

    const reaction = await this.reactionService.removeReaction(data.id)
    const msg = await this.messageService.removeReaction(data.id, data.messageId);
  }

  async onCreateRoom(
    @ConnectedSocket() socket: Socket,
    @MessageBody() data: { user: UserDto; room: RoomDto; members: Array<any>, subdomain: string },
  ) {
    console.log(' data: ', data.subdomain);
    const createdRoom = await this.roomService.createRoom(
      data.room,
      data.user,
      data.members,
      data.subdomain
    );

    this.logService.log('created room: ', createdRoom);
    const roomId = createdRoom?.id ? createdRoom?.id?.toString() : createdRoom['_id']?.toString();
    socket.join(roomId);
    const payload = new EventPayloadDto();
    payload.event = MESSAGE_EVENT.CREATE_CIRCLE,
      payload.data = createdRoom;

    await this.addMembersToSocketRoom(createdRoom.users, roomId);
    this.server.to(roomId).emit(EVENT_NAME, payload);
  }
  async addMembersToSocketRoom(users: string[] | UserDto[], roomId: string) {
    console.log('users in room: ', users, 'roomId: ', roomId)
    for (let i = 0; i < users.length; i++) {
      let userId;
      if (typeof users[i] === 'string') {
        userId = users[i];
      } else {
        const user = users[i] as UserDto;
        userId = user.id ? user.id : user['_id']
      }
      const userSockets = this.users.get(userId);
      if (userSockets && userSockets.length > 0)
        userSockets.map((socket, idx) => {
          console.log('user socket id @index : ', idx, socket.id);
          socket.join(roomId);
        })
    }
  }

  async createNewUser(
    @MessageBody() userData,
    @ConnectedSocket() socket: Socket,
  ) {
    // to create user if not exist
    console.log('new user: ', userData, socket.id);
    const result = await this.userService.create(userData);
    // this.logService.log('result: ', result);
    const payload = new EventPayloadDto();
    payload.event = MESSAGE_EVENT.NEW_USER,
      payload.data = result;
    // console.log('new user payload: ', payload, socket.id)
    setTimeout(() => {
      this.server.to([socket.id]).emit(EVENT_NAME, payload);
    }, 100);
    console.log('created user sent: ',)
  }

  async getMyChannels(
    @ConnectedSocket() socket: Socket,
    @MessageBody() user: { id: string, subdomain: string },
  ) {
    try {
      console.log('requesting channels: ', user);
      if (user.id || user.subdomain) {
        const rooms = await this.roomService.getRoomsForUser(
          user?.id,
          user.subdomain,
        );

        rooms.forEach(async (room) => {
          const roomId = room.id ? room.id : room._id
          console.log('room id: ', roomId);
          await socket.join(roomId);
        });

        console.log('socket rooms: ', socket.rooms);
        const payload = new EventPayloadDto();
        payload.event = MESSAGE_EVENT.REQUEST_CHANNELS,
          payload.data = rooms;
        this.server.to(socket.id).emit(EVENT_NAME, payload);


      } else {
        console.log(' user does not have id: ', user);
      }
      this.checkUserSocket(socket, user.id);

    } catch (error) {
      this.logService.error('error requesting channels: ', error)
    }
  }
  async onJoinRoom(
    @ConnectedSocket() socket: Socket,
    @MessageBody() data: { userId: string; roomId: string },
  ) {
    try {
      const room = await this.roomService.getOnlyRoomData(data.roomId);
      if (room) {
        await this.roomService.addUserToRoom(data.roomId, data.userId);
        await this.roomService.addRoomToUser(data.userId, data.roomId);
        room.users = [...room.users, data.userId] as string[];

        console.log('room to join: ', data.roomId);
        socket.join(data.roomId)
        const payload = new EventPayloadDto();
        payload.event = MESSAGE_EVENT.JOIN_CIRCLE;
        payload.data = await this.roomService.getOnlyRoomData(data.roomId);;
        this.server.to(data.roomId).emit(EVENT_NAME, payload);
        // this.server.socketsJoin(data.roomId);


      }
    } catch (error) {
      console.log('error : ', error.message);
    }
  }
  async addUsersToChannel(
    @MessageBody() data: AddUserToRoomDto,
    @ConnectedSocket() socket: Socket,
  ) {
    try {
      this.logService.log('adding users to circle: ', data);
      const users = await this.roomService.addUsersToRoom(data);

      console.log('found members: ', users)
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.ADD_USERS_TO_CIRCLE,
        payload.data = users;
      for (let i = 0; i < users.length; i++) {
        const userSockets = this.users.get(users[i].id);
        userSockets?.forEach(socket => {
          socket.join(data.roomId)
        })
      }
      // this.logService.log('room members: ', users)
      this.server.to(data.roomId).emit(EVENT_NAME, payload);
    } catch (error) {
      this.logService.error('Error adding users to circle: ', error)
    }
  }

  async onLeaveRoom(@ConnectedSocket() socket: Socket) {
    // remove connection from JoinedRooms
    // await this.joinedRoomService.deleteBySocketId(socket.id);
  }

  async onNewMessage(
    @ConnectedSocket() socket: Socket,
    @MessageBody() message: CreateMessageDto,
  ) {
    this.logService.log('new message: ', message);
    try {
      this.checkUserSocket(socket, message.user['id'] ? message.user['id'] : message.user)

      let messageType = MESSAGE_TYPE.TEXT;
      if (message?.files?.length && !message?.text?.length) {
        messageType = MESSAGE_TYPE.FILE;
      }
      if (message?.files?.length && message?.text?.length) {
        messageType = MESSAGE_TYPE.TEXT_WITH_FILE;
      }
      message.messageId = v4();


      // console.log('createdMessae: ', createdMessage);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.NEW_MESSAGE,
        payload.data = message;

      if (message.is_private) {

        const userSockets = this.getUserSocketIds(message.receiverId.toString());
        const senderSockets = this.getUserSocketIds(message.user['id']);

        this.logService.log("user sockets: ", userSockets, senderSockets,);

        this.server
          .to([...userSockets, ...senderSockets])
          .emit(EVENT_NAME, payload);
      } else {
        if (typeof message.room !== 'string') {
          const roomId = message.room['id']
          this.server
            .to([roomId.toString()])
            .emit(EVENT_NAME, payload);
        } else {
          this.server
            .to([message?.room?.toString()])
            .emit(EVENT_NAME, payload);
        }
      }
      const tobSaved = {
        ...message,
        user: message.user['id'],
        room: message.room ? message['room']['id'] : null,
        messageType,
      }

      this.messageService.create(tobSaved).then(async (createdMessage: any) => {
        console.log('saved msg: ', createdMessage)
        const logData = new ActivityLogDto();
        logData.UserId = createdMessage?.user?.user_id;
        logData.Application = 'Joble',
          logData.By = createdMessage?.user?.user_id;
        logData.ActivitySummary = '' + createdMessage?.user?.username + ` sent a message to ${message.is_private ? createdMessage?.receiverId?.username?.toString() : message?.room['name']} `// + message.is_private ? createdMessage?.receiverId?.username?.toString() : message?.room['name'];
        logData.Description = '' + createdMessage?.user?.username + ` sent a message  ${message.is_private ? createdMessage?.receiverId?.username.toString() : message?.room['name']}`// + message.is_private ? createdMessage?.receiverId?.username.toString() : message?.room['name'];
        logData.EventCategory = EventCategory.Chat;
        logData.EventId = createdMessage?.id;
        console.log('check: ', message.is_private ? createdMessage?.receiverId?.username?.toString() : message?.room['name']);
        const logRes = await this.activityLogService.logActivity(logData);
        console.log('logRes: ', logRes);
      });
    } catch (error) {
      this.logService.error('Error in message: ', error);
    }
  }

  async getPrivateChats(
    @ConnectedSocket() socket: Socket,
    @MessageBody() data: { connectionId: string; userId: string },
  ) {
    try {
      console.log('getting private messages: ', data);
      const messages = await this.messageService.getPrivateMessages(data);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.GET_PRIVATE_MESSAGES,
        payload.data = messages;
      // console.log('Private messages: ', messages)
      this.server.to(socket.id).emit(EVENT_NAME, payload);
      this.checkUserSocket(socket, data.userId);
    } catch (error) {
      this.logService.error('error getting private messages: ', error.message);
      return error;
    }
  }

  async getPrivateChatsByPage(
    @ConnectedSocket() socket: Socket,
    @MessageBody() data: GetPrivateMessageByPageDto,
  ) {
    try {
      console.log('getting private messages: ', data);
      const messages = await this.messageService.getPrivateMessagesByPage(data);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.GET_PRIVATE_MESSAGES_BY_PAGE,
        payload.data = messages;
      // console.log('Private messages: ', messages)
      this.server.to(socket.id).emit(EVENT_NAME, payload);
      this.checkUserSocket(socket, data.userId); // Q1 - why check ing socket user
    } catch (error) {
      this.logService.error('error getting private messages: ', error.message);
      return error;
    }
  }


  async typing(
    @MessageBody() typingDto: { isTyping: boolean; roomId: string; name: string, connectionId: string },
    @ConnectedSocket() socket: Socket,
  ) {
    const { isTyping, roomId, name, connectionId } = typingDto;
    const payload = new EventPayloadDto();
    payload.event = MESSAGE_EVENT.TYPING,
      payload.data = typingDto;
    if (roomId)
      socket.broadcast.to(roomId).emit(EVENT_NAME, payload);
    if (connectionId) {
      const userSockets = this.getUserSocketIds(connectionId);
      this.server.to(userSockets).emit(EVENT_NAME, payload);
    }
  }
  async deleteMessageMe(
    @MessageBody() data: { message: MessageDto; userId: string },
    @ConnectedSocket() socket: Socket,
  ) {
    try {
      console.log('deleting for self: ', data);
      const saved = await this.userService.deleteMessage(data);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.DELETE_MESSAGE_ME,
        payload.data = saved
      this.server.to(socket.id).emit(EVENT_NAME, payload);
    } catch (error) {
      this.logService.log('Error deleting message: ', error)
    }
  }
  async deleteMessageAll(@MessageBody() message: MessageDto) {
    try {
      console.log('deleting all message: ', message);
      const deleted = await this.messageService.markMessageDeleted(message);
      if (deleted) {
        console.log('deleted messageId: ', deleted.room);
        const payload = new EventPayloadDto();
        payload.event = MESSAGE_EVENT.DELETE_MESSAGE_ALL,
          payload.data = deleted
        this.server.to(deleted.room.toString()).emit(EVENT_NAME, payload);
      }
    } catch (error) {
      this.logService.error('error deleting all messages: ', error)
    }
  }
  async getAllUnReadMessages(
    @ConnectedSocket() socket: Socket,
    @MessageBody() data: { userId: string, subdomain: string },
  ) {
    try {
      console.log('getting all unread: ', data)
      const unRead = await this.messageService.getAllUnReadMessages(data.userId, data.subdomain);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.GET_ALL_UNREAD_MESSAGES,
        payload.data = unRead;
      this.server.to(socket.id).emit(EVENT_NAME, payload);
    } catch (error) {
      this.logService.error('Error getting all unread messages')
    }
  }
  async updateReadStatus(
    @ConnectedSocket() socket: Socket,
    @MessageBody() data: { userId: string; messageId: string },
  ) {
    try {
      const readStatus = await this.messageService.updateReadStatus(data);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.UPDATE_READ_STATUS,
        payload.data = readStatus
      this.server.to(socket.id).emit(EVENT_NAME, payload);
    } catch (error) {
      this.logService.error('error updating read status: ', error)
    }
  }


  async addUserToChannel(
    @MessageBody() data: any,
    @ConnectedSocket() socket: Socket,
  ) {
    console.log('add to channel: ', data);
    const result = await this.roomService.addUserToRoom(
      data.channelId,
      data.userId,
    );
    Logger.log(`added to room: ${result}`);

    const payload = new EventPayloadDto();
    payload.event = MESSAGE_EVENT.ADD_USER_TO_CIRCLE,
      payload.data = {
        message: 'User successfully added to channel',
        channel: result,
      }
    this.server.to(socket.id).emit(EVENT_NAME, payload);
  }



  async chatCall(
    @ConnectedSocket() socket: Socket,
    @MessageBody() chatcall: ChatCallDto,
  ) {
    try {
      // console.log('chat call: ', chatcall, socket.id);
      chatcall.callId = v4();
      // console.log('initiating chat call: ', chatcall, this.users, socket.rooms);
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.CHAT_CALL,
        payload.data = chatcall;
      let userSocketIds: string | string[];
      if (chatcall.type == CHAT_CALL_TYPE.GROUP) {
        // this.server.to(chatcall.meetingId).emit(EVENT_NAME, payload);
        userSocketIds = chatcall.meetingId;
      }

      if (chatcall.type == CHAT_CALL_TYPE.PRIVATE) {

        userSocketIds = this.getUserSocketIds(chatcall.meetingId);
      }
      // userSockets = this.users.get(chatcall.meetingId);
      console.log('usersockets: ', userSocketIds)
      socket.broadcast.to(userSocketIds).emit(EVENT_NAME, payload);
      // register call here

    } catch (error) {
      console.log('call error: ', error)
    }
  }

  async chatCallResponse(
    @ConnectedSocket() socket: Socket,
    @MessageBody() callData: ChatCallDto,
  ) {
    try {
      // console.log('chat call data: ', callData)
      const payload = new EventPayloadDto();
      payload.event = MESSAGE_EVENT.CHAT_CALL_RESPONSE;
      if (!callData.callId) {
        callData.callId = v4()
      }

      const user = await this.userService.findOne(callData.callerId);
      let receiver;
      // if (callData.receiverId)
      // receiver = await this.userService.findOne(callData.receiverId.toString());
      // if (callData.status == CHAT_CALL_STATUS.DECLINED) {



      // updated call log
      if (callData.status == CHAT_CALL_STATUS.DECLINED)
        this.chatCallService.create({
          callerId: user.id.toString(),
          receiverId: callData.type == CHAT_CALL_TYPE.PRIVATE ? receiver : null,
          callerName: user.username,
          meetingId: callData.meetingId,
          title: callData.title,
          type: callData.type,
          name: callData.name,
          format: callData.format,
          profilePictureUrl: callData.profilePictureUrl,
          status: CHAT_CALL_STATUS.DECLINED,
          callDuration: 0,
          messageId: '',
          callId: callData.callId
        });

      if (callData.status == CHAT_CALL_STATUS.ACCEPTED) {
        // update call log
        callData.startTime = callData.startTime ? callData.startTime : Date.now();
        console.log(' call has been acepted: ', callData.startTime)
        this.chatCallService.create({
          callerId: user.id.toString(),
          receiverId: callData.type == CHAT_CALL_TYPE.PRIVATE ? receiver : null,
          callerName: user.username,
          meetingId: callData.meetingId,
          title: callData.title,
          type: callData.type,
          name: callData.name,
          format: callData.format,
          profilePictureUrl: callData.profilePictureUrl,
          status: CHAT_CALL_STATUS.ACCEPTED,
          callDuration: 0,
          startTime: Date.now(),
          messageId: '',
          callId: callData.callId
        });
        this.server.to(callData.callerSocket).emit(EVENT_NAME, { event: MESSAGE_EVENT.CHAT_CALL_RESPONSE, data: callData })
      }
      if (callData.status == CHAT_CALL_STATUS.ENDED) {
        callData.endTime = Date.now();

        const call = await this.chatCallService.findByCallId(callData.callId);
        // console.log('call found: ', call)
        if (call.participantCount == 0) {
          console.log('Call has already ended'); return;
        }
        if (call.participantCount > 1) {
          call.participantCount--;
          this.chatCallService.create(call);
        } else {

          const call = await this.chatCallService.create({ ...callData, endTime: Date.now(), participantCount: 0 })

          // console.log('found call: ', call)
          const duration = (new Date(call.endTime).getTime() - new Date(call.startTime).getTime()) / 1000//this.formatTimeDuration((call.endTime.getTime() - call.startTime.getTime()) / 1000);
          const text = `Your ${callData.format} call ${callData.type == CHAT_CALL_TYPE.GROUP ? ' from ' + callData.callerName : ' '} lasted for ${duration}`;

          console.log('duration: ', duration, ' start time: ', callData.startTime);
          const callMessage = await this.messageService.createCallMessage({
            user: user.id,
            messageType: MESSAGE_TYPE.CALL,
            room: callData.type == CHAT_CALL_TYPE.GROUP ? callData.meetingId : null,
            receiverId:
              callData.type == CHAT_CALL_TYPE.PRIVATE ? callData.receiverId : null,
            is_private: false,
            messageId: callData.callId,
            duration: duration?.toString(),
            callType: callData.format
          });
          const msgPayload = new EventPayloadDto();
          msgPayload.event = MESSAGE_EVENT.NEW_MESSAGE;

          let userSocketIds;
          msgPayload.data = callMessage;
          if (callData.type == CHAT_CALL_TYPE.GROUP) {
            // this.server.to(chatcall.meetingId).emit(EVENT_NAME, payload);
            userSocketIds = callData.meetingId;
          }
          if (callData.type == CHAT_CALL_TYPE.PRIVATE) {

            userSocketIds = this.getUserSocketIds(callData.meetingId);
          }
          console.log('message payload: ', msgPayload)
          this.server.to(userSocketIds).emit(EVENT_NAME, msgPayload);

          this.chatCallService.create(callData);


          const logData = new ActivityLogDto();

          logData.UserId = user.user_id;
          logData.Application = 'Joble';
          logData.By = callData.callerId;
          logData.ActivitySummary =
            callData.callerName + ' is calling ' + callData.title;
          logData.Description =
            callData.callerName +
            ' made a ' +
            callData.type +
            ' call to ' +
            callData.title;
          logData.EventCategory = EventCategory.Chat;
          logData.EventId = callData.meetingId;

          this.activityLogService.logActivity(logData);

        }
        payload.data = callData;
        // console.log('chat call response payload: ', payload)
        setTimeout(() => {
          socket.to(callData.callerSocket).emit(EVENT_NAME, payload);
        }, 300);

      }
    } catch (error) {
      this.logService.error('error saving chat call response: ', error)
    }
  }
  getUserSocketIds(userId: string) {

    const sockets = this.users.get(userId);
    // console.log('sockets found: ', sockets, ' userId used: ', userId);
    if (sockets?.length)
      return sockets?.map(socket => socket.id)
    return [];
  }

  checkUserSocket(socket: Socket, userId: string) {
    try {
      this.logService.log('socket and userId: ', socket.id, userId);
      if (this.users.has(userId)) {
        if (!this.users.get(userId).find(sockt => sockt.id == socket.id))
          this.users.get(userId).push(socket);
      } else {
        this.users.set(userId, [socket]);
      }
      // this.logService.log('user sockets: ', this.users.get(userId))
    } catch (error) {
      console.log('error checking user socket id')
    }
  }

  formatTimeDuration(seconds: number) {
    console.log('secondes: ', seconds)
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secondsRemaining = Math.floor(seconds % 60);

    const formattedHours = hours > 0 ? `${hours} hours` : '';
    const formattedMinutes = minutes > 0 ? `${minutes} minutes` : '';
    const formattedSeconds = secondsRemaining > 0 ? `${secondsRemaining} seconds` : '';
    console.log('calculated duration: ', `${formattedHours} ${formattedMinutes} ${formattedSeconds}`.trim())
    return `${formattedHours} ${formattedMinutes} ${formattedSeconds}`.trim();
  }
}


