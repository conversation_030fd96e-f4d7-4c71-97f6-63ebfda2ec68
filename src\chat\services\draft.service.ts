/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DraftDto } from 'src/dto/dtos';
import { Draft } from 'src/models/draft.model';

@Injectable()
export class DraftService {

    constructor(@InjectModel(Draft.name) private readonly draftModel: Model<Draft>) { }
    async createDraft(draft: DraftDto) {
        const found = await this.getDraft(draft.chatId, draft.jobProUserId);
        if (found) {
            found.text = draft.text;
            found.files = draft.files;
            await found.save();
            return await this.getDraft(draft.chatId, draft.jobProUserId);;
        }
        const saved = await this.draftModel.create(draft);
        await saved.save();
        return await this.getDraft(draft.chatId, draft.jobProUserId);
    }
    async getDraft(chatId: string, jobProUserId: string) {
        return await this.draftModel.findOne({ chatId, jobProUserId })
    }
    async deleteDraft(chatId: string, jobProUserId: string) {
        const res = await this.draftModel.deleteOne({ chatId, jobProUserId });
        if (res.deletedCount) return { message: 'Draft deleted', status: 1 };
        return { message: 'Failed to delete draft', status: 0 };
    }
}
