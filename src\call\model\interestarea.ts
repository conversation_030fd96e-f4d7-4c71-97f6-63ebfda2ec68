import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
@Schema({
    timestamps: true,
    virtuals: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class InterestArea {
    @Prop() interests: [{ type: String }];
    @Prop() userId: string;
    @Prop() eventId: string;
    @Prop() name: string;
}
export const InterestAreaSchema = SchemaFactory.createForClass(InterestArea)