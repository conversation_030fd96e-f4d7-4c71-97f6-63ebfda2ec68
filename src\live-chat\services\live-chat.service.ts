/*
https://docs.nestjs.com/providers#services
*/

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ChatStatus, LiveChatDto, LiveChatMessage, LiveChatUserDto } from '../dtos';
import { Model } from 'mongoose';
import { LiveChat, LiveChatUser, LiveMessage } from '../models';
import { JwtService, JwtSignOptions } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { LogService } from 'src/log/log.service';

@Injectable()
export class LiveChatService {

    constructor(@InjectModel(LiveChatUser.name) private liveChatUserModel: Model<LiveChatUser>,
        @InjectModel(LiveMessage.name) private readonly liveMessageModel: Model<LiveMessage>,
        @InjectModel(LiveChat.name) private readonly liveChatModel: Model<LiveChat>,
        private logService: LogService,
        private jwtService: JwtService, private configService: ConfigService) { }

    async getChatMessages(chatId: any) {
        if (!chatId) throw new BadRequestException('chat id is required to get existing messages for a chat')
        const messages = await this.liveMessageModel.find({ chatId }).populate({

            path: 'senderId',
            model: 'LiveChatUser'

        });
        // this.logService.log('existing messages: ', messages);
        return messages;

    }

    async getOpenChats() {
        const chats = await this.liveChatModel.find({ $or: [{ status: ChatStatus.open }, { status: ChatStatus.active }] }).populate({
            path: 'clientId',
            model: 'LiveChatUser'
        }).populate({
            path: 'messages',
            model: 'LiveMessage',
            populate: {
                path: 'senderId',
                model: 'LiveChatUser'
            }
        }).sort({ status: -1 })
        this.logService.log('chats: ', chats);

        return chats;
    }
    async getActiveChats() {
        const chats = await this.liveChatModel.find({ status: ChatStatus.active }).populate({
            path: 'clientId',
            model: 'LiveChatUser'
        }).populate({
            path: 'messages',
            model: 'LiveMessage',
            populate: {
                path: 'senderId',
                model: 'LiveChatUser'
            }
        })
        this.logService.log('chats: ', chats);
        return chats;
    }
    async getUserById(id: any) {
        const found = await this.liveChatUserModel.findOne({ _id: id });
        this.logService.log('user:', found);
        return found;
    }
    async registerUser(guestUser: LiveChatUserDto) {
        this.logService.log('live chat user: ', guestUser)

        let foundUser = await this.liveChatUserModel.findOne({ email: guestUser.email });
        if (!foundUser) {
            foundUser = await (await this.liveChatUserModel.create(guestUser)).save();
        } else {
            this.liveChatUserModel.updateOne({ email: guestUser.email }, { ...guestUser })
        }
        this.logService.log('found user: ', foundUser);
        const options: JwtSignOptions = {
            secret: this.configService.get('AUTH_SCREET'),
            expiresIn: '2hr'
        }
        const token = this.jwtService.sign({ email: foundUser.email, phone: foundUser.phone, id: foundUser.id }, options);
        return {
            accessToken: token,
            user: foundUser
        }
    }
    async updateUserProfilePicture(userId: string, profilePicture: string, firstName: string, lastName: string) {
        if (!profilePicture) throw new BadRequestException('user id and profile picture url are required');

        const user = await this.liveChatUserModel.findOne({ _id: userId });
        if (!user) throw new NotFoundException('user with the specified id does not exist');
        const data = { profilePicture: profilePicture };
        if (firstName) data['firstName'] = firstName;
        if (lastName) data['lastName'] = lastName;
        await this.liveChatUserModel.updateOne({ _id: userId }, data);

        return await this.getUserById(user.id)
    }
    async startChat(chatDto: LiveChatDto) {
        try {
            const foundChat = await this.liveChatModel.findOne({ $or: [{ clientId: chatDto.clientId, status: ChatStatus.open }, { clientId: chatDto.clientId, status: ChatStatus.active }] }).populate({
                path: 'clientId',
                model: 'LiveChatUser'
            }).populate({
                path: 'messages',
                model: 'LiveMessage',
                populate: {
                    path: 'senderId',
                    model: 'LiveChatUser'
                }
            })
            this.logService.log('chat: ', foundChat)
            if (foundChat) return foundChat;
            delete chatDto.id;
            const create = await this.liveChatModel.create({ ...chatDto, status: ChatStatus.open });
            const saved = await create.save();
            this.logService.log('chat 1: ', saved)
            return await saved.populate({
                path: 'clientId',
                model: 'LiveChatUser'
            });
        } catch (error) {
            this.logService.log('Error creating chat: ', error);
            throw error;
        }
    }
    async joinChat(supportRepId: string, chatId: string) {
        try {
            const foundChat = await this.liveChatModel.findById(chatId);
            if (foundChat) {
                const rep = await this.getUserByJobId(supportRepId.trim());
                this.logService.log('found rep: ', rep, supportRepId)

                if (rep) await this.liveChatModel.updateOne({ _id: chatId }, { supportRepIds: rep.id, status: ChatStatus.active });
            }
            return await this.getChatById(chatId)
        } catch (error) {
            this.logService.log('Error joining chat: ', error)
            throw error;
        }
    }
    async getUserByJobId(supportRepId: string) {
        this.logService.log('supportRepId: ', supportRepId)
        if (!supportRepId) throw new BadRequestException('Support Rep id is required')
        const user = await this.liveChatUserModel.findOne({ jobId: supportRepId });
        return user;
    }
    async getChatWithMessages(chatId: string) {
        if (!chatId) throw new BadRequestException('chat id is required to get a chat by its id');
        return await this.liveChatModel.findOne({ _id: chatId })
            .populate({
                path: 'clientId',
                model: 'LiveChatUser'
            })
            .populate({
                path: 'supportRepId',
                model: 'LiveChatUser'
            })
            .populate({
                path: 'messages',
                model: 'LiveMessage',
                populate: {
                    path: 'senderId',
                    model: 'LiveChatUser'
                }
            })
    }
    async getChatById(chatId: string) {

        if (!chatId) throw new BadRequestException('chat id is required to get a chat by its id');
        return await this.liveChatModel.findOne({ _id: chatId }).populate({
            path: 'clientId',
            model: 'LiveChatUser'
        }).populate({
            path: 'supportRepId',
            model: 'LiveChatUser'
        }).populate({
            path: 'messages',
            model: 'LiveMessage',
            populate: {
                path: 'senderId',
                model: 'LiveChatUser'
            }
        })

    }
    async leaveChat(chatId: string, supportRepId: string) {
        if (!chatId || !supportRepId) throw new BadRequestException('client id and chat id are required');
        const found = await this.liveChatModel.findById(chatId);
        if (found) {
            await this.liveChatModel.updateOne({ _id: chatId }, { status: ChatStatus.open, $pull: { supportRepIds: supportRepId } })
        }
    }
    async addNewMessage(chatId: string, message: LiveChatMessage) {
        if (!chatId) throw new BadRequestException('chat id is required');

        const found = await this.liveChatModel.findById(chatId);
        const msg: LiveMessage = { content: message.content, messageType: message.messageType.toString(), files: message.files, senderId: message.senderId, createdAt: new Date(), chatId: message.chatId, isSystemGenerated: false }
        const messag = await this.liveMessageModel.create(msg);
        const savedMsg = await messag.save();
        // found.messages.push(savedMsg.id);
        await this.liveChatModel.updateOne({ _id: found.id }, { $push: { messages: savedMsg.id } });
        // this.logService.log('saved message: ', savedMsg);
        return savedMsg.populate({ model: 'LiveChatUser', path: 'senderId' });
    }
    async updateMessage(chatId: string, message: LiveChatMessage) {
        if (!chatId) throw new BadRequestException('chat id is required');
        const chat = await this.liveChatModel.findById(chatId);

    }
    async closeChat(chatId: string, supportRepId: string) {
        if (!chatId || !supportRepId) throw new BadRequestException('chat id and supportRepId are required');
        const update = await this.liveChatModel.updateOne({ _id: chatId }, { status: ChatStatus.closed });

        return await this.getChatById(chatId);

    }
} 