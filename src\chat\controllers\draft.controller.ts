/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DraftService } from '../services/draft.service';
import { ChatUserDto, DraftDto } from 'src/dto/dtos';
import { GetUser } from 'src/get-user.decorator';

@ApiTags('draft')
@Controller('v2/draft')
export class DraftController {
    constructor(private readonly draftService: DraftService) { }

    @Post('create')
    createDraft(@Body() draft: DraftDto) {
        return this.draftService.createDraft(draft)
    }
    @Get('/:id')
    getDraft(@Param('id') chatId: string, @GetUser() user: ChatUserDto) {
        return this.draftService.getDraft(chatId, user.jobProUserId)
    }
}
