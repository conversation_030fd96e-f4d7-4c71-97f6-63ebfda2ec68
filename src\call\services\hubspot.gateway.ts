
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import {
    ConnectedSocket,
    MessageBody,
    OnGatewayConnection,
    OnGatewayDisconnect,
    SubscribeMessage,
    WebSocketGateway,
    WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { EventService } from 'src/call/services/event.service';
import { AnnouncementDto } from '../dto/announcement.dta';
import { ScheduleEventDto } from '../dto/eventschedule.dto';
import * as ss from 'socket.io-stream'
import { EVENT, EVENTS, Payload } from '../events.enum';
import { BackstageUserDto } from '../dto/backstageuser.dto';

@WebSocketGateway(
    {
        cors: {
            origin: '*',
        },
        maxHttpBufferSize: 3e8,
        // namespace: 'events'
    })
export class HubspotGateway implements OnGatewayConnection, OnGatewayDisconnect {
    @WebSocketServer()
    server: Server;
    events: Map<string, Set<{ ticketId: string, socketId: string }>> = new Map();
    hubspots: Map<string, any[]> = new Map();
    usersInBackstage: Map<string, Set<BackstageUserDto>> = new Map();
    constructor(private eventService: EventService) { }
    async handleConnection(@ConnectedSocket() socket: Socket, ...args: any[]) {

        console.log('connection id: ', socket.id,)
        socket.emit('send-join-request');
        const query = socket.handshake.query;
        console.log('query: ', query);
        if (query.eventId != 'null' && query.userId != 'null') {
            // await this.eventService.registerParticipant({ eventId: query.eventId, userId: query.userId, name: query.name, socketId: socket.id })
            console.log('joing event: ', query.eventId)
            socket.join(query.eventId);
            this.addUserToEvent(query?.eventId?.toString(), query?.userId?.toString(), socket.id);
        }

    }
    @SubscribeMessage(EVENT)
    handleEvents(@ConnectedSocket() socket: Socket, @MessageBody() payload: Payload) {
        console.log('events payload: ', payload)
        switch (payload.event) {
            case EVENTS.JOIN_BACKSTAGE:
                this.joinBackstage(socket, payload.data);
                break;
            case EVENTS.GET_BACKSTAGE_MEMBERS:
                this.getBackstageMembers(socket, payload.data);
                break;
            case EVENTS.NEW_BACKSTAGE_USER:
                this.handleNewBackstageUser(payload.data)
                break;
            case EVENTS.IS_TYPING:
                this.isTyping(socket, payload)
                break;
            case EVENTS.JOIN_MEETING:
                this.joinMeeting(socket, payload)
            default:
                break;
        }
    }
    joinMeeting(socket: Socket, payload: Payload) {
        const { schedule, receiver } = payload.data;
        const userSockets = this.getSocketIdByTicketId(receiver);
        this.server.to(userSockets).emit(EVENT, payload)
    }
    getBackstageMembers(socket, eventId: string) {
        const members = this.getUsersInBackStage(eventId);
        const payload: Payload = {
            event: EVENTS.GET_BACKSTAGE_MEMBERS,
            data: members
        }
        this.server.to(socket.id).emit(EVENT, payload)
    }
    handleNewBackstageUser(data: any) {

    }

    async handleDisconnect(@ConnectedSocket() socket: Socket) {
        console.log('id to disconnect: ', socket.id);
        try {
            let eventId = this.removeUserBySocketId(socket.id);
            const removed = await this.eventService.removeParticipantBySocketId(socket.id);
            // console.log('removed: ', removed);
            console.log('eventId: ', eventId);
            if (removed) {
                eventId = eventId ?? removed['eventId'];
                let userId = removed['userId'];
                console.log('userId: ', userId)
                console.log('user left:', eventId, userId);
                this.server.to(eventId).emit('user-left-event', { eventId: eventId, userId: userId })
                socket.leave(eventId);
            }

        } catch (error) {
            console.log('error id disconnect: ', error.message);
        }
    }
    @SubscribeMessage('share-file')
    shareFile(@ConnectedSocket() socket: Socket, @MessageBody() data: any) {
        console.log('chunk: ', data)
        this.server.to(data.roomId).emit('share-file', data)
    }
    @SubscribeMessage('client-closing')
    clientClosing(@MessageBody() data: { eventId: string, userId: string, source?: string }) {
        console.log('client closing: ', data)
        this.server.to(data.eventId).emit('user-left-event', data)
    }
    @SubscribeMessage('ignore-accept')
    async inviteResponse(
        @MessageBody()
        data: ScheduleEventDto,
        @ConnectedSocket() socket: Socket,
    ) {
        const updateRes = await this.eventService.updateInvite(data);
        console.log('update response: ', updateRes)
        const socketId = this.getSocketIdByTicketId(data.userId);
        console.log('socketId of sender: ', socketId)
        this.server.to([socketId, socket.id]).emit('invite-response', data)
    }
    @SubscribeMessage('create-announcement')
    async createAnnouncement(@MessageBody() data: AnnouncementDto,
        @ConnectedSocket() socket: Socket) {
        console.log('new announcement')
        const announcement = await this.eventService.createAnnouncement(data);
        console.log('announcement created: ', announcement);
        const event = this.events.get(data.eventId);
        console.log('event: ', event);
        if (event) {
            this.server.to(data.eventId).emit('new-announcement', announcement);
        } else {
            const socketIds = (await this.eventService.getAllParticipants(data.eventId)).map((user) => user['socketId'])
            // console.log('socketIds: ', socketIds);
            this.server.to(socketIds).emit('new-announcement', announcement);
        }
    }
    @SubscribeMessage('new-event-user')
    async newEventUser(@MessageBody() data: any,
        @ConnectedSocket() socket: Socket,) {
        // console.log('signal new user: ', data, socket?.id);
        const event = this.events.get(data?.eventId);
        // console.log('event: ', event, ' Event user: ', data);
        socket.join(data?.eventId);
        const newUserRes = await this.eventService.registerParticipant({ ...data, socketId: socket.id });
        // console.log('new user res: ', newUserRes);
        setTimeout(() => {
            this.server.to(data?.eventId).emit('new-event-user', data);
            this.server.to(socket.id).emit('event-users', newUserRes);
        }, 2000);

    }
    @SubscribeMessage('schedule-event')
    async scheduleEvent(
        @MessageBody()
        data: ScheduleEventDto,
        @ConnectedSocket() socket: Socket,
    ) {

        try {

            console.log('data to create: ', data)
            const result = await this.eventService.scheduleMeeting(data);
            console.log("scheduled event: ", result)
            const event = this.events.get(data.eventId);
            console.log('event', event.values())
            let socketId = this.getSocketIdByTicketId(data.participantId);
            if (!socketId) {
                socketId = (await this.eventService.getParticipantByUserId(data.participantId)).toJSON()['socketId'];
            }
            console.log('user socketId: ', socketId, socket.id)
            if (result)
                this.server.to([socketId, socket.id]).emit('schedule-event', { status: 'success', ...result['_doc'] });
        } catch (error) {
            console.log("error: ", error);
            socket.to(socket.id).emit('schedule-event', { error: error, status: 'failed' });
        }
    }
    @SubscribeMessage('private-message')
    async privateMessage(@ConnectedSocket() socket: Socket, @MessageBody() data) {
        console.log('private message: ', data);

        let receiverSocket = this.getSocketIdByTicketId(data.receiverSid);
        console.log('receiver socKet @: ', receiverSocket);
        if (!receiverSocket && data.receiverSid) {

            const participant = await this.eventService.getParticipantByUserId(data.receiverSid);
            receiverSocket = participant ? participant['socketId'] : '';
            console.log('receiver socket 1: ', receiverSocket);
            this.server.to([receiverSocket, socket.id]).emit('private-message', data);
        } else { this.server.to([receiverSocket, socket.id]).emit('private-message', data) };
    }
    // @SubscribeMessage('is-typing')
    async isTyping(socket: Socket, data: Payload,) {
        // { ticketId: any; isTypeing: boolean; receiverId?: string; hubspotId?: string; eventId?: string, }
        console.log('is typing: ', data);
        if (data.data.receiverId) {
            let receiverSocket = this.getSocketIdByTicketId(data.data.receiverId);
            console.log('rec socket: ', receiverSocket, ' receiverId: ', data.data.receiverId, 'eventId: ', data.data?.eventId);

            if (!receiverSocket) {
                const participant = await this.eventService.getParticipantByUserId(data.data.receiverId);
                receiverSocket = participant['socketId'];
                console.log('rec socket 1: ', receiverSocket);

                this.server.to(receiverSocket).emit(EVENT, data);
            }
            { this.server.to(receiverSocket).emit(EVENT, data); }
        }
        if (data.data.eventId) {
            socket.join(data.data.eventId)
            this.server.to(data.data.eventId).emit(EVENT, data);
        }
        if (data.data.hubspotId) {
            this.server.to(data.data.hubspotId).emit(EVENT, data);
        }
    }

    @SubscribeMessage('joinEvent')
    async joinEvent(
        @MessageBody()
        data: any,
        @ConnectedSocket() socket: Socket,
    ) {
        console.log('adding user to event: ', data, ' socketId: ', socket.id);
        this.addUserToEvent(data.eventId, data.ticket, socket.id);
        const result = this.events.get(data.eventId).values();
        console.log('result: ', [...result]);
        socket.join(data.eventId);
        const res = await this.eventService.registerParticipant({ eventId: data.eventId, userId: data.ticket, socketId: socket.id, ...data })

        setTimeout(() => {
            this.server.to(data.eventId).emit('new-event-user', res)
        }, 3000);
    }
    @SubscribeMessage('join-hubspot')
    joinLeaveHubspot(
        @MessageBody()
        data: { hubspotId: string; hubspot: string; ticketId: string },
        @ConnectedSocket() socket: Socket,
    ) {
        console.log('join or leave hubspot: ', data);
        const hubspot = this.hubspots.get(data.hubspotId);
        console.log('hubspot found: ', hubspot);
        socket.join(data.hubspotId);
        if (this.hubspots.has(data.hubspotId)) {
            const found = hubspot.find((userId) => userId.ticketId == data.ticketId);
            // console.log('found user: ', found, data)
            if (!found && data.ticketId) hubspot.push({ ticketId: data.ticketId, socketId: socket.id });
            this.hubspots.set(data.hubspotId, hubspot);
            console.log('already has a user: ', hubspot);
        } else {
            this.hubspots.set(data.hubspotId, [{ ticketId: data.ticketId, socketId: socket.id }]);
            console.log('hubspots: ', this.hubspots);
        }
        console.log(' hubspot: ', this.hubspots);
        this.server.to(data.hubspotId).emit('user-joined-hubspot', data);
        this.server.to(socket.id).emit('getusers-in-hubspot', hubspot);
    }
    @SubscribeMessage('leave-hubspot')
    leaveHubspot(
        @MessageBody()
        data: { hubspotId: string; hubspot: string; ticketId: string },
        @ConnectedSocket() socket: Socket,
    ) {
        const hubspot = this.hubspots.get(data.hubspotId);
        const ticketIndex = hubspot.findIndex((id: string) => id == data.ticketId);
        if (ticketIndex != -1) {
            hubspot.splice(ticketIndex, 1);
            this.hubspots.set(data.hubspotId, hubspot);
            this.server.to(data.hubspotId).emit('user-left-hubspot', data);
        }
    }
    @SubscribeMessage('notify-matched-user')
    async notifyMatchedUser(@ConnectedSocket() socket: Socket, @MessageBody() data: any) {
        console.log('matching user: ', data);
        let recSocket = this.getSocketIdByTicketId(data.userId);
        if (!recSocket) {
            const user = await this.eventService.getParticipantByUserId(data.userId);
            recSocket = user['socketId'];
            console.log('matching socket: ', recSocket)
        }
        console.log('recSocket: ', recSocket)
        this.server.to(recSocket).emit('notify-matched-user', data);
    }
    @SubscribeMessage('user-in-hubspot')
    getUsersInHubspot(@MessageBody() hubspotId: string) {
        const hubspotUsers = this.hubspots.get(hubspotId);
        console.log('hubspot users: ', hubspotUsers, hubspotId);
        this.server.to(hubspotId).emit('getusers-in-hubspot', hubspotUsers);
    }
    @SubscribeMessage('send-hubspot-users')
    sendHubSpotUsers(
        @ConnectedSocket() socket: Socket,
        @MessageBody() hubspots: string[] | string,
    ) {
        console.log('hubspot: ', hubspots);
        const data = {};

        console.log('hubspot with data: ', data);
        this.server.to(socket.id).emit('receive-hubspot-users', this.hubspots);
    }
    @SubscribeMessage('join-backstage')
    joinBackstage(@ConnectedSocket() socket: Socket, @MessageBody() data: BackstageUserDto) {
        console.log('joining backstage: ', data, socket.id)
        data['socketId'] = socket.id;

        const backstageId = data.eventId + '_backstage';
        this.addUserToBackstage(backstageId, data)
        const payload: Payload = {
            event: EVENTS.NEW_BACKSTAGE_USER,
            data: data
        }

        const usersInBackstage = this.getUsersInBackStage(data.eventId);
        console.log('users in backstage: ', usersInBackstage)
        const senderPayload: Payload = {
            event: EVENTS.GET_BACKSTAGE_MEMBERS,
            data: usersInBackstage
        }
        this.server.to(backstageId).emit(EVENT, payload);
        socket.join(backstageId);

        this.server.to(socket.id).emit(EVENT, senderPayload)
    }
    @SubscribeMessage('meet-now-response')
    async meetNowResponse(@MessageBody() data: { meetingId: string; userId: string, senderName: string, senderId: string, status: string }) {
        let senderSocket = this.getSocketIdByTicketId(data.senderId);
        console.log('sender socket: ', senderSocket);
        if (senderSocket) {
            const user = await this.eventService.getParticipantByUserId(data.senderId);
            senderSocket = user['socketId'];
        }
        this.server.to(senderSocket).emit('meet-now-response', data)
    }
    @SubscribeMessage('meet-now')
    async meetNow(@MessageBody() data: { meetingId: string; userId: string, senderName: string, senderId: string }) {
        let userSocket = this.getSocketIdByTicketId(data.userId);
        console.log('meet now: ', userSocket, 'data: ', data);
        if (!userSocket) {
            const user = await this.eventService.getParticipantByUserId(data.userId);
            userSocket = user['socketId'];
        }
        console.log('user socket: ', userSocket);
        this.server.to(userSocket).emit('meet-now', data);

    }
    @SubscribeMessage('new-hubspot-message')
    sendHubspotMessage(@MessageBody() data: { message: any; hubspotId: string }) {
        console.log('new message data: ', data);
        this.server.to(data.hubspotId).emit('new-hubspot-message', data.message);
    }
    getSocketIdByTicketId = (ticketId: string): string | undefined => {

        for (const [, eventSet] of this.events) {
            // console.log('event set:', eventSet);
            for (const eventData of eventSet) {
                // console.log('event data:', eventData);
                if (eventData.ticketId === ticketId) {
                    return eventData.socketId;
                }
            }
        }
        return undefined; // Ticket ID not found
    };
    removeUserBySocketId = (socketId: string): any => {
        for (const [, eventSet] of this.events) {
            for (const eventData of eventSet) {
                if (eventData.socketId === socketId) {
                    const res = eventSet.delete(eventData);
                    console.log('res: ', res)
                    return eventSet;
                }
            }
        }
        return undefined; // Ticket ID not found
    };
    // events for live event in eventglo
    @SubscribeMessage('lobby-message')
    async lobbyMessage(@MessageBody() data: { message: any, eventId: string }, @ConnectedSocket() socket: Socket) {
        this.addUserToEvent(data.eventId, data.message.senderTicketId, socket.id);
        socket.join(data.eventId)
        const event = this.events.get(data.eventId);

        console.log('lobby message: ', data, ' event object: ', event);

        if (event?.size === 0) {
            const participants = await this.eventService.getAllParticipants(data.eventId);
            const sockets = participants.map((part: any) => part.socketId);
            console.log(' sockets: ', sockets);
            this.server.to(sockets).emit('lobby-message', data.message)
        } else
            this.server.to(data.eventId).emit('lobby-message', data.message);
    }
    @Cron(CronExpression.EVERY_30_SECONDS)
    async processSchedules() {
        // console.log('scheduling time');
        try {
            const schedules = await this.eventService.getSchedules();
            const today = new Date();
            schedules?.forEach(async (schedule) => {
                const scheduleDate = new Date(schedule['date']);
                // console.log('today and schedule date:', today.getDate(), new Date(schedule['date']))

                if (scheduleDate.getDate() == today.getDate()) {
                    // console.log('is today: ', schedule);
                    // console.log('schedule timezone: ', schedule['timeZone']);
                    const targetTime = schedule['startTime']; // Specify the target time in the format "HH:mm"
                    const targetTimeSplit = targetTime.split(":");
                    const targetHours = parseInt(targetTimeSplit[0], 10);
                    const targetMinutes = parseInt(targetTimeSplit[1], 10);

                    const currentHours = today.getHours();
                    const currentMinutes = today.getMinutes();




                    const timeDifference = (currentHours * 60 + currentMinutes) - (targetHours * 60 + targetMinutes);
                    console.log('currentHour: ', currentHours, 'currentM: ', currentMinutes, ' targetH: ', targetHours, ' targetM: ', targetMinutes);
                    console.log(' diff: ', timeDifference)
                    if (Math.abs(timeDifference) <= 5) {
                        let recSocket = this.getSocketIdByTicketId(schedule['participantId']);
                        if (!recSocket) {
                            recSocket = await this.eventService.getParticipantByUserId(schedule['participantId'])['socketId']
                        }
                        let senderSocket = this.getSocketIdByTicketId(schedule['userId']);
                        if (!senderSocket) {
                            senderSocket = await this.eventService.getParticipantByUserId(schedule['userId'])['socketId'];
                        }
                        console.log('sockets: ', senderSocket, recSocket)
                        this.server.to([recSocket, senderSocket]).emit('current-meeting', schedule);
                        // this.server.emit('current-meeting', schedule);
                        console.log("The target time is within a 5-minute range of the current time.");
                    } else {
                        console.log("The target time is NOT within a 5-minute range of the current time.");
                    }
                }
            })
        } catch (error) {
            console.log('error in schedule')
        }
    }
    getUsersInBackStage(backstage: string) {
        // return this.usersInBackstage.get(backstage);
        const usersArray: BackstageUserDto[] = Array.from(this.usersInBackstage.get(backstage) || new Set());
        return usersArray;

    }
    addUserToBackstage(backstage: string, user: BackstageUserDto) {
        // console.log('adding to backstage: ', backstage)
        if (this.usersInBackstage.has(user.eventId)) {
            const userSet = this.usersInBackstage.get(user.eventId);
            let found = null;
            for (const obj of userSet) {
                if (obj.id == user.id) {
                    found = obj;
                    break;
                }
            }
            console.log('found: ', found);
            if (found) {
                userSet.delete(found);
            }
            userSet.add(user);
        } else {
            const usersSet = new Set([user]);

            this.usersInBackstage.set(user.eventId, usersSet);
        }
        console.log('all: ', this.usersInBackstage)
    }
    addUserToEvent = (eventId: string, ticketId: string, socketId: string) => {
        // Check if the event already exists in the map
        if (this.events.has(eventId)) {
            // Retrieve the existing set for the event
            const eventSet = this.events.get(eventId);
            let found = null;
            for (const obj of eventSet) {
                if (obj.ticketId == ticketId) {
                    found = obj;
                    break;
                }
            }
            console.log('found: ', found);
            if (found) {
                eventSet.delete(found);
            }
            // Add the new object to the set
            eventSet.add({ ticketId, socketId });
        } else {
            // Create a new set with the initial object
            const eventSet = new Set([{ ticketId, socketId }]);

            // Add the event set to the map
            this.events.set(eventId, eventSet);
        }
    };
    @SubscribeMessage('end-event')
    endEvent(@ConnectedSocket() socket: Socket, @MessageBody() eventId: string) {
        console.log('host has ended event: ', eventId);
        this.server.to(eventId).emit('end-event', eventId);
    }
}
