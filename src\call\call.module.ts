/* eslint-disable prettier/prettier */
/*
https://docs.nestjs.com/modules
*/

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserModule } from 'src/user/user.module';
import { EventController } from './controllers/event.controller';
import { RecordingController } from './controllers/recording.controller';
import { EventService } from './services/event.service';
import { RecordingService } from './services/recording.service';
// import { RoomService } from './services/room.service';
import { MessagesModule } from 'src/messages/messages.module';
import { Announcement, AnnouncementSchema } from './model/announcement.model';
import { EventParticipantSchema } from './model/event_participant';
import { UserAvailabilitySchema } from './model/user-availability';
import { HubspotGateway } from './services/hubspot.gateway';
import { LogModule } from 'src/log/log.module';
import { InterestArea, InterestAreaSchema } from './model/interestarea';
@Module({
    imports: [LogModule,
        UserModule,
        MessagesModule,
        MongooseModule.forFeature([
            { name: 'UserAvailability', schema: UserAvailabilitySchema },
            { name: 'EventParticipant', schema: EventParticipantSchema },
            { name: Announcement.name, schema: AnnouncementSchema },
            { name: InterestArea.name, schema: InterestAreaSchema },
        ])
    ],
    controllers: [
        RecordingController,
        EventController
    ],
    providers: [
        EventService,
        RecordingService,

        HubspotGateway
    ],
})
export class CallModule { }

