import { IoAdapter } from "@nestjs/platform-socket.io";
import { createAdapter } from "@socket.io/redis-streams-adapter";
import { ServerOptions } from "http";
import { createClient } from "redis";
import { Logger } from "@nestjs/common";

const pubClient = createClient({ url: process.env.REDIS_SERVER, password: process.env.REDIS_PASSWORD });


export class RedisIoStreamAdapter extends IoAdapter {
    private adapterConstructor: ReturnType<typeof createAdapter>;
    private readonly logger = new Logger(RedisIoStreamAdapter.name);

    async connectToRedis(): Promise<void> {
        try {
            await pubClient.connect();
            this.adapterConstructor = createAdapter(pubClient);
            this.logger.log(`Redis Adapter connected`);
        } catch (error) {
            this.logger.error(`Error connecting to Redis: ${error}`);
        } finally {
            if (pubClient.isOpen) {
                this.logger.log(`Redis Adapter connected and ready`);
            }
        }
    }

    createIOServer(port: number, options?: ServerOptions): any {
        const server = super.createIOServer(port, options);
        server.adapter(this.adapterConstructor);
        return server;
    }
}