import { HttpmessageService } from './chat/services/httpmessage.service';
import { UrlPreviewModule } from './url-preview/url-preview.module';
import { LiveChatModule } from './live-chat/live-chat.module';
import { SearchService } from './chat/services/search.service';
import { ChatModule } from './chat/chat.module';
import { RabbitmqModule } from './rabbitmq/rabbitmq.module';
import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { ScheduleModule } from '@nestjs/schedule';
import { ActivityLogModule } from './activity-log/activity-log.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import config from './config/configuration';
import { FileModule } from './file/file.module';
import { AppExceptionsFilter } from './filters/app.exception.filter';
import { LogModule } from './log/log.module';
import { MailModule } from './mail/mail.module';
import { UserModule } from './user/user.module';
import { join } from 'path';
import { ServeStaticModule } from '@nestjs/serve-static';
import { JwtService } from '@nestjs/jwt';
import { JwtAuthGuard } from './user/jwt.guard';
import { PassportModule } from '@nestjs/passport';
import { RedisService } from './chat/redis.service';
// import { ClusterService } from './clutering.service';



@Module({
  imports: [
    UrlPreviewModule,
    LiveChatModule,
    RabbitmqModule,
    LogModule,
    ActivityLogModule,
    MailModule,
    // CallModule,
    ScheduleModule.forRoot(),
    MulterModule.register({
      dest: './uploads',
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'), // Adjust the path based on your project structure
      serveRoot: '/public',
    }),
    FileModule,
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],

    }),
    // MessagesModule,
    PassportModule,
    UserModule,
    ChatModule,
    ConfigModule.forRoot({
      isGlobal: true,
      load: [config],
    }),
  ],
  controllers: [
    AppController
  ],
  providers: [
    HttpmessageService,
    SearchService,
    RedisService,
    AppService,
    JwtService,
    {
      provide: APP_FILTER,
      useClass: AppExceptionsFilter,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    // ClusterService
  ],
})
export class AppModule {
  // If you need to configure middleware, you can use configure() method
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply()
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
