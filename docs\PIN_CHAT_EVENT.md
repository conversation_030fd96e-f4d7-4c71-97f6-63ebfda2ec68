# PIN_CHAT Event Implementation

## Overview
The `PIN_CHAT` event allows users to pin or unpin entire chat conversations, making them easily accessible at the top of their chat list.

## Event Details
- **Event Name**: `PIN_CHAT`
- **Event Type**: `CHAT_EVENTS.PIN_CHAT`
- **Socket Event**: `chat-event`

## Payload Structure

### Request Payload
```typescript
{
  event: 'PIN_CHAT',
  data: {
    chatId: string,      // Required: ID of the chat to pin/unpin
    isPinned: boolean,   // Required: true to pin, false to unpin
    pinnedBy?: string    // Optional: User ID who is pinning (auto-filled from socket user)
  }
}
```

### Response Payload
```typescript
{
  event: 'PIN_CHAT',
  data: {
    message: string,           // Success/failure message
    chat: Chat,               // Updated chat object
    isPinned: boolean,        // Current pin status
    pinnedBy: string | null   // User who pinned the chat (null if unpinned)
  },
  error?: string             // Error message if operation failed
}
```

## Usage Examples

### JavaScript/TypeScript Client
```javascript
// Pin a chat
socket.emit('chat-event', {
  event: 'PIN_CHAT',
  data: {
    chatId: '64f1a2b3c4d5e6f7g8h9i0j1',
    isPinned: true
  }
});

// Unpin a chat
socket.emit('chat-event', {
  event: 'PIN_CHAT',
  data: {
    chatId: '64f1a2b3c4d5e6f7g8h9i0j1',
    isPinned: false
  }
});

// Listen for responses
socket.on('chat-event', (payload) => {
  if (payload.event === 'PIN_CHAT') {
    if (payload.error) {
      console.error('Pin chat failed:', payload.error);
    } else {
      console.log('Pin chat success:', payload.data.message);
      console.log('Chat is now pinned:', payload.data.isPinned);
    }
  }
});
```

### Testing with HTML Test Client
1. Open `src/socket_test.html` in a browser
2. Connect to the socket server
3. Enter a valid Chat ID
4. Click the "📌 Pin Chat" button
5. Check the console for the response

## Database Changes
The following fields were added to the Chat model:
- `isPinned: boolean` (default: false)
- `pinnedBy: string` (user ID who pinned the chat)

## Implementation Details

### Files Modified
1. `src/messages/dto/event.payload.dto.ts` - Added PIN_CHAT event
2. `src/models/chat.model.ts` - Added isPinned and pinnedBy fields
3. `src/chat/services/chat.service.ts` - Added pinUnpinChat method
4. `src/chat/services/chat.gateway.ts` - Added pinUnpinChat handler
5. `src/dto/dtos.ts` - Added pin fields to ChatDto
6. `src/dto/pin-chat.dto.ts` - Created PinChatDto
7. `src/socket_test.html` - Added test button and function

### Error Handling
The implementation includes comprehensive error handling:
- Validates required fields (chatId, userId)
- Handles database errors
- Returns appropriate error messages
- Emits error responses to the client

### Security
- Requires user authentication (uses WSAuthGuard)
- Only allows users to pin/unpin chats they have access to
- Validates user permissions through the existing chat membership system

## Integration Notes
- The PIN_CHAT event is available in both authenticated and test modes
- Pinned chats should be displayed at the top of chat lists in the UI
- The pin status is broadcast to all users in the chat
- Pin status persists across sessions
