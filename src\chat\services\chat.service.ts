/*
https://docs.nestjs.com/providers#services
*/

import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { NotFoundError } from 'rxjs';
import { ChatDto, ChatUserDto, CreateChatUserDto, UpdateChatDto } from 'src/dto/dtos';
import { ChatUser } from 'src/models/chat-user.model';
import { CHAT_TYPE, Chat, EncryptedKey } from 'src/models/chat.model';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { ChatMessageService } from './chat-message.service';
import { ChatInviteService } from './chat-invite.service';
import crypto from 'crypto';
import { LogService } from 'src/log/log.service';


@Injectable()
export class ChatService {


    constructor(
        @InjectModel(Chat.name) private readonly chatModel: Model<Chat>,
        private chatUserService: ChatuserService,
        private chatMessageService: ChatMessageService,
        private chatInviteService: ChatInviteService,
        private logService: LogService
    ) {
        // this.addIsArchievedToChats()

    }
    async checkUserIsAChatMember(jobProUserId: string, chatId: any) {
        const hasJoined = await this.chatModel.findOne({ _id: chatId, jobProUserIds: jobProUserId });
        return hasJoined;
    }
    async deleteChat(chatId: string): Promise<any> {
        return await this.chatModel.deleteOne({ _id: chatId })
    }
    async addIsArchievedToChats() {
        const chats = await this.chatModel.find({});
        chats.forEach(async (chat) => {
            await this.chatModel.updateOne({ _id: chat.id }, { isArchived: false });
            if (chat.chatType == CHAT_TYPE.DM) {
                const updatePrivate = await this.chatModel.updateOne({ _id: chat.id }, { isPrivate: true });
                // this.logService.log('updatePrivate status: ', updatePrivate)
            }
        })
        const allDms = await this.chatModel.find({ chatType: 'DM' });
        this.logService.log('all DMs: ')
            , allDms
    }
    async findDMChatByUserIds
        (userId: string, collaboratorId: string, tenantId: string) {
        const foundDM = (await this.chatModel.findOne({ chatType: CHAT_TYPE.DM, users: { $all: [userId, collaboratorId], $size: 2 } }, { encryptedKeys: 0, symmetricKey: 0 }))
        if (!foundDM) {
            const jobProUserIds = (await Promise.all([this.chatUserService.findUser(userId), this.chatUserService.findUser(collaboratorId)])).map((user) => user.jobProUserId);
            const newDM = await (await this.chatModel.create({ users: [userId, collaboratorId], jobProUserIds, chatType: CHAT_TYPE.DM, tenantId })).save();
            return newDM;
        }
        return foundDM;
    }
    async changeGroupChatToCircle
        (chatId: string, name: string, isPrivate: boolean, jobProUserIds: string[]) {
        const userIds = (await Promise.all(jobProUserIds.map(id => this.chatUserService.findUserByJobProId(id)))).map(u => u.id);
        await this.chatModel.updateOne(
            { _id: chatId },
            {
                name: name,
                chatType: CHAT_TYPE.CIRCLE,
                $push: { users: { $each: userIds }, jobProUserIds: { $each: jobProUserIds } },
                isPrivate: isPrivate
            }
        );
        return await this.findChatByIdPopulated(chatId)
    }
    async findChatsByUserId(userId: string, tenantId: string) {
        const chats = await this.chatModel.find({ $or: [{ users: userId, tenantId }, { tenantId, isGeneral: true }] });
        return chats;
    }
    async findPublicChats(tenantId: string) {
        const chats = await this.chatModel.find({ tenantId, isPublic: true });
        return chats;
    }
    async getAiTrainingChat(tenantId: string) {
        const trainerChat = await this.chatModel.findOne({ tenantId: tenantId, name: 'AI Training' });
        const chatMessages = await this.chatMessageService.findAllMessagesForAChat(trainerChat.id);
        const aiTraningChat = {
            name: trainerChat.name,
            createdAt: trainerChat.createdAt,
            chatId: trainerChat.id,
            messages: []
        }
        for (const message of chatMessages) {
            const formatted = {
                'sender': { 'jobId': message.sender['jobProUserId'], 'chatUserId': message.sender['id'], },
                text: message.text,
                'files': message.files
            }
            aiTraningChat.messages.push(formatted);
        }
        // {
        //     createdAt: Date, chatId: string, name: string, messages: [{
        //         sender: {
        //             "jobProUserId": string, chatUserId: string
        //         } text?: string, files?: [{ url: string, type: string }]
        //     }]
        // } 
        return aiTraningChat;
    }
    async cleanDuplicateUserInChat() {
        try {
            const chats = await this.chatModel.find({});
            for (let i = 0; i < chats.length; i++) {
                let users = [];
                for (const user of chats[i].users) {
                    users.push(user.toString());

                }
                this.logService.log('users 0: ', users)
                users = [...new Set(users)];
                for (const user of users) {
                    const foundUser = await this.chatUserService.findUser(user);
                    if (foundUser) {
                        const userObjs = await this.chatUserService.findUserObjectsByJobProId(foundUser.jobProUserId);
                        if (userObjs.length > 1) {
                            const userToKeep = userObjs[0];
                            const ids = [];
                            for (const toDel of userObjs) {
                                if (userToKeep.id != toDel.id) {
                                    // delete after picking it's id
                                    ids.push(toDel.id);
                                }
                            }
                            await this.chatUserService.deleteManyUsers(ids);

                            // find all the chat that the deleted users are members and replace the id of the one left
                            const chatsToUpdate = await this.chatModel.find({ users: { $in: ids } })

                            const updates = chatsToUpdate.map(chat => ({
                                updateOne: {
                                    filter: { _id: chat._id },
                                    update: {
                                        $set: {
                                            users: chat.users.map(user => ids.includes(user) ? userToKeep.id : user)
                                        }
                                    }
                                }
                            }));
                            await this.chatModel.bulkWrite(updates);
                        }
                    }
                }
                this.logService.log('users: ', users)
                chats[i].jobProUserIds = [...new Set(chats[i].jobProUserIds)];
                chats[i].users = users;
                await chats[i].save();
            }
            return await this.chatModel.find({});
        } catch (error) {
            this.logService.error('Error cleaning duplicates: ', error);
        }
    }
    async findUniqueTenantIds(): Promise<string[]> {
        const result = await this.chatModel.aggregate([
            {
                $group: {
                    _id: '$tenantId',
                },
            },
            {
                $project: {
                    _id: 0,
                    tenantId: '$_id',
                },
            },
        ]);

        return result.map(r => r.tenantId);
    }
    async createCircleForAllWorkspace(circle: string) {
        const tenantIds = await this.findUniqueTenantIds();
        const chats = [];
        for (const tenantId of tenantIds) {
            this.logService.log('TenantId: ', tenantId);
            const user = await this.chatUserService.findAUserWithTenantId(tenantId);
            if (user != null) {
                const AiChatObj = new ChatDto();
                AiChatObj.chatType = CHAT_TYPE.CIRCLE;
                AiChatObj.tenantId = tenantId;
                AiChatObj.jobProUserIds = [user.jobProUserId]
                AiChatObj.users = [user.id];
                AiChatObj.name = circle;
                AiChatObj.isPrivate = true;
                AiChatObj.isGeneral = false;
                AiChatObj.description = 'A circle for training the Ai for an organization workspace!';
                AiChatObj.createdBy = user.id;
                const exist = await this.chatModel.findOne({ tenantId, name: circle })
                if (!exist) {
                    const AiChat = await this.createChat(AiChatObj);

                    this.addUserToChat(user.jobProUserId, user.email, AiChat.id, AiChat);
                    chats.push(AiChat);
                } else {
                    this.logService.log(circle + ' circle already exist!')
                }
            }
        }
        return chats;
    }

    async createSymetricKeyForParticipant() {
        try {
            const chats = await this.chatModel.find({});
            for (let i = 0; i < chats.length; i++) {
                this.logService.log('index: ', i)
                const symmetricKey = crypto.randomBytes(32).toString('hex'); // 256-bit symmetric key

                const encryptedKeys = await Promise.all(
                    chats[i].users.map(async (userId) => {
                        const user = await this.chatUserService.findUser(userId);
                        let encryptedKey;
                        if (user.publicKey) { encryptedKey = this.encryptWithPublicKey(user.publicKey, symmetricKey); } else {
                            const { publicKey, privateKey } = await this.chatUserService.createKeys(userId);
                            encryptedKey = this.encryptWithPublicKey(publicKey, symmetricKey);
                        }
                        const enc = new EncryptedKey();
                        enc.encryptedKey = encryptedKey;
                        enc.userId = userId.toString();
                        return enc;
                    }))
                this.logService.log('Encrypted key for chat: ', chats[i].id, encryptedKeys);
                const chatUp = await this.chatModel.updateOne({ _id: chats[i].id }, { encryptedKeys, symmetricKey: symmetricKey })
                this.logService.log('chat update: ', chatUp, chats[i].id);

            }
            return await this.chatModel.find({});
        } catch (error) {
            this.logService.log('Error with key: ', error)
        }
    }
    async archiveChat(chatId: string, userId: string) {
        try {
            if (!chatId)
                throw new BadRequestException(
                    'chat id is required to archive a chat',
                );
            if (!userId)
                throw new BadRequestException(
                    'user id is required to archive a chat',
                );

            const up = await this.chatModel.findOneAndUpdate(
                { _id: chatId },
                { isArchived: true, archivedBy: userId },
            );

            // await this.activityLogService.logActivity({
            //   EventCategory: EventCategory.Chat,
            //   ActivitySummary: up.name + ' circle has been archived',
            //   Description: 'The circle has been un archived',
            //   EventId: channelId,
            //   UserId: userId,
            //   Application: 'Joble',
            //   By: userId,
            // });

            if (up) {
                return { message: 'chat has been successfully archived' };
            }
            return {
                message: 'chat archiving failed',
            };
        } catch (error) {
            this.logService.log('Archive error: ', error);
            throw error;
        }
    }
    async unArchieveChat(chatId: string, userId: string) {
        try {
            if (!chatId)
                throw new BadRequestException(
                    'Channel id is required to un-archive a channel',
                );
            const up = await this.chatModel.findOneAndUpdate(
                { _id: chatId },
                { isArchived: true },
            );
            // const activityLog = new ActivityLogDto()
            // await this.activityLogService.logActivity({
            //   EventCategory: EventCategory.Chat,
            //   ActivitySummary: up.name + ' channel has been unarchived',
            //   Description: 'The channel has been un archived',
            //   EventId: channelId,
            //   UserId: userId,
            //   Application: 'Joble',
            //   By: userId,
            // });
            if (up) {
                return { message: 'The channel has been successfully un-archived' };
            }
            return { message: 'un-archiving failed' };
        } catch (error) {
            this.logService.log('error: ', error);
            throw error;
        }
    }

    async pinUnpinChat(chatId: string, userId: string, isPinned: boolean) {
        try {
            if (!chatId) {
                throw new BadRequestException('Chat ID is required to pin/unpin a chat');
            }
            if (!userId) {
                throw new BadRequestException('User ID is required to pin/unpin a chat');
            }

            const updateData: any = { isPinned };
            if (isPinned) {
                updateData.pinnedBy = userId;
            } else {
                updateData.pinnedBy = null;
            }

            const updatedChat = await this.chatModel.findOneAndUpdate(
                { _id: chatId },
                updateData,
                { new: true }
            );

            if (updatedChat) {
                const message = isPinned
                    ? 'Chat has been successfully pinned'
                    : 'Chat has been successfully unpinned';
                return {
                    message,
                    chat: updatedChat,
                    isPinned: updatedChat.isPinned,
                    pinnedBy: updatedChat.pinnedBy
                };
            }

            return {
                message: isPinned ? 'Chat pinning failed' : 'Chat unpinning failed',
            };
        } catch (error) {
            this.logService.error('Pin/Unpin chat error: ', error);
            throw error;
        }
    }
    async getArchivedChats(userId: string) {
        try {
            if (!userId)
                throw new BadRequestException(
                    'user id is required to get archived channels',
                );
            const channels = await this.chatModel.find({
                users: userId,
                isArchived: true,
            }, { symmetricKey: 0, encryptedKeys: 0 });
            return channels;
        } catch (error) {
            this.logService.log('Error getting archieved chat: ', error);
            throw error;
        }
    }

    async addUsersToGeneralChat() {
        const generalChats = await this.chatModel.find({ isGeneral: true });
        for (let i = 0; i < generalChats.length; i++) {
            const users = await this.chatUserService.findUsersByTenantId(generalChats[i].tenantId.toString());
            const userIds: string[] = [];
            const jobProUserIds: string[] = [];
            for (const user of users) {
                userIds.push(user.id);
                jobProUserIds.push(user.jobProUserId);
            }
            await this.chatModel.updateOne({ _id: generalChats[i].id }, {
                $addToSet: {
                    users: { $each: userIds },
                    jobProUserIds: { $each: jobProUserIds }
                }
            })
        }
        return await this.chatModel.find({ isGeneral: true });
    }

    async addUserToGeneralChat(tenantId: string, userId: string, jobProUserId: string) {
        const generalChat = await this.chatModel.findOne({ tenantId, isGeneral: true });
        if (generalChat) {
            await this.chatModel.updateOne({ _id: generalChat, users: { $ne: userId }, jobProUserIds: { $ne: jobProUserId } }, { $push: { users: userId, jobProUserIds: jobProUserId } })
        }
    }

    async getChatFiles(chatId: string, userId: string) {
        try {
            if (!chatId) throw new BadRequestException('chat id is required to retrieve chat files')
            const userExistInChat = await this.chatModel.findOne({ id: chatId, users: userId });
            if (userExistInChat) throw new BadRequestException('user does not belong to the chat')
            const files = await this.chatMessageService.getChatFiles(chatId)
            return files;
        } catch (error) {
            this.logService.log('Error getting files for chat: ', chatId)
        }
    }

    /**
     * 
     * @param data of chat dto
     * @returns ChatDto object
     */
    async createChat(data: ChatDto) {
        try {
            this.logService.log('data for creating chat: ', data)
            if (data.tenantId && data.tenantId != 'undefined' && !data.jobProUserIds.includes('undefined')) {
                // const symmetricKey = crypto.randomBytes(32).toString('hex'); // 256-bit symmetric key
                const jobProUserIds = [...new Set(data.jobProUserIds)];
                const userIds = (await this.chatUserService.findUsersByJobProIds(jobProUserIds, data.tenantId,
                    data.emails))?.map(user => user.id);
                this.logService.log('userIds: ', userIds);
                // const encryptedKeys = await Promise.all(
                //     userIds.map(async (userId) => {
                //         const user = await this.chatUserService.findUser(userId);
                //         const encryptedKey = this.encryptWithPublicKey(user.publicKey, symmetricKey);
                //         return { userId, encryptedKey };
                //     })
                // );
                // this.logService.log('symetric keys: ', symmetricKey, 'encryptedKeys: ', encryptedKeys)

                this.logService.log('mapped userIds: ', userIds);
                this.logService.log(' chat data: ', data)
                if (data.chatType == CHAT_TYPE.CIRCLE) {
                    const chatExistWithName = await this.chatModel.findOne({
                        tenantId: data.tenantId,
                        name: { $exists: true, $eq: data.name },
                        // users: { $size: userIds.length, $all: userIds.map(id => new Types.ObjectId(id)) }
                    }, { encryptedKeys: 0 }).populate({
                        path: 'createdBy',
                        model: 'ChatUser',
                        select: '-privateKey -publicKey'
                    }).populate({
                        path: 'users',
                        model: 'ChatUser',
                        select: '-privateKey -publicKey'
                    });
                    // this.logService.log('chat found: ', chatExistWithName);
                    if (chatExistWithName) {

                        const messages = await this.chatMessageService.getPaginatedMessages(chatExistWithName.id)
                        return {
                            ...chatExistWithName.toJSON(),
                            ...messages
                        }
                    }
                }
                // if (chatExistWithName) {
                //     throw new ConflictException('Chat with the same name already exists for the Workspace');
                // }
                const uniqueUserIds = [...new Set(userIds)];
                const userIdsObj = uniqueUserIds?.map(id => new Types.ObjectId(id));
                this.logService.log('user id objs: ', userIdsObj)
                if (data.chatType == CHAT_TYPE.DM) {
                    const chatExistWithUsers = await this.chatModel.findOne({
                        tenantId: data.tenantId,
                        users: {
                            $all: userIdsObj,
                            $size: userIdsObj.length
                        },
                        chatType: data.chatType,
                    }).populate({
                        path: 'createdBy',
                        model: 'ChatUser',
                        select: '-privateKey -publicKey'
                    },).populate({
                        path: 'users',
                        model: 'ChatUser',
                        select: '-privateKey -publicKey'
                    });
                    if (chatExistWithUsers) {

                        const messages = await this.chatMessageService.getPaginatedMessages(chatExistWithUsers.id)
                        return {
                            ...chatExistWithUsers.toJSON(),
                            ...messages
                        }
                    }

                }
                if (data.chatType == CHAT_TYPE.DM) data.isPrivate = true;
                const newChat = new this.chatModel({ ...data, users: uniqueUserIds, });
                const chat = await newChat.save();
                delete chat.encryptedKeys;
                this.logService.log('new chat: ', chat);
                for (const user of userIds) {
                    this.chatUserService.addChatToUser(chat.id, user)
                }

                chat.lastMessageTimestamp = Date.now();
                await chat.save();

                const chatRes = await (await chat.populate({
                    path: 'createdBy',
                    model: 'ChatUser', select: '-privateKey -publicKey'
                })).populate(
                    {
                        path: 'users',
                        model: 'ChatUser', select: '-privateKey -publicKey'
                    });



                return chatRes;
            }
        } catch (error) {
            this.logService.log('Error creating chat: ', error)
            throw error;
        }
    }
    /**
     * 
     * @param userId 
     * @param jobProUserId 
     * @param chatId 
     */
    async addUserToChat(jobProUserId: string, chatId: string, email: string, chat: ChatDto) {
        try {

            let user = await this.chatUserService.findUserByJobProId(jobProUserId);

            if (!chat) throw new NotFoundException(' Chat not found!')

            if (!user) {
                // create user
                user = await this.chatUserService.createUser({ jobProUserId: jobProUserId, tenantId: chat.tenantId, email });
            }
            this.logService.log('user found adding to chat: ', user);
            // const encryptedKey = this.encryptWithPublicKey(user?.publicKey, chat?.symmetricKey);
            // const userEnc = { userId: user.id, encryptedKey }
            const up = await this.chatModel.updateOne({ _id: chatId, users: { $ne: new Types.ObjectId(user.id) } },
                { $push: { users: new Types.ObjectId(user.id), jobProUserIds: jobProUserId, } });
            this.chatUserService.addChatToUser(chatId, user.id);

            this.logService.log('update chat: ', up)
        } catch (error) {
            this.logService.log('error joining circle: ', error)
        }
    }

    /**
     * 
     * @param userIds 
     * @param jobProUserIds 
     * @param chatId 
     * @returns 
     */
    async addUsersToChat(jobProUserIds: string[], emails: string, chatId: string, name: string) {
        const chat = await this.chatModel.findOne({ _id: chatId.trim() }, { encryptedKeys: 0, symmetricKey: 0 });
        if (chat.chatType == CHAT_TYPE.DM) {
            await this.chatModel.updateOne({ _id: chat.id }, { name });
        }

        await this.chatModel.updateOne({})
        for (let i = 0; i < jobProUserIds.length; i++) {
            await this.addUserToChat(jobProUserIds[i], chatId, emails[i], chat);
        }
        return (await this.chatModel.findOne({ _id: chat.id }, { encryptedKeys: 0, symmetricKey: 0 }).populate({
            path: 'users',
            model: 'ChatUser'
        }
        ).populate({
            path: 'createdBy',
            model: 'ChatUser'
        }
        )).toJSON()
    }

    async removeUserFromChat(chatId: string, jobProUserId: string, name: string, userId: string) {
        if (!chatId || !jobProUserId) throw new BadRequestException('user id, job pro user and chat id are required')
        const user = await this.chatUserService.findUserByJobProId(jobProUserId);
        this.logService.log('found user in remove from chat: ', user)
        const up = await this.chatModel.updateOne({ _id: chatId }, { name: name, $pull: { users: new Types.ObjectId(user.id), jobProUserIds: jobProUserId, 'encryptedKeys': { userId: userId } } });
        this.logService.log('remove user update: ', up);
        this.chatUserService.removeChatFromUser(user.id, chatId);

        return (await this.chatModel.findOne({ _id: chatId }, { encryptedKeys: 0 })
            .populate({
                path: 'users',
                model: 'ChatUser'
            }
            ).populate({
                path: 'createdBy',
                model: 'ChatUser'
            }
            )).toJSON()
    }
    
    async findChatByIdPopulated(chatId: string): Promise<ChatDto> {
        this.logService.log('chat id: ', chatId)
        if (!chatId) throw new BadRequestException('chat id is required to get chat by id')
        const found = await this.chatModel.findOne({ _id: chatId }, { encryptedKeys: 0 }).populate({
            path: 'users',
            model: 'ChatUser'
        }
        ).populate({
            path: 'createdBy',
            model: 'ChatUser'
        });

        if (!found) throw new NotFoundException('Chat not found!');

        return found?.toJSON();
    }

    async findChatById(chatId: string): Promise<ChatDto> {
        try {
            this.logService.log('chat id: ', chatId)
            console.log('chat id: ', chatId)
            if (!chatId) throw new BadRequestException('chat id is required to get chat by id')
            const found = await this.chatModel.findOne({ _id: chatId }, { encryptedKeys: 0, symmetricKey: 0 }).populate({
                path: 'createdBy',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            },
            ).populate({
                path: 'users',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            })

            if (!found) throw new NotFoundException('Chat not found!');

            return found?.toJSON();
        } catch (error) {
            this.logService.log('Error gettting chat: ', error.message)
        }
    }

    async findChatsForUser(userId: string, tenantId: string) {
        if (!userId) throw new BadRequestException('user id is required to load chats');
        return await this.chatModel.find({
            $and: [{ tenantId: tenantId, isArchived: false },
            { $or: [{ users: userId, isArchived: false }, { isPrivate: false, isArchived: false }] }]
        }, { encryptedKeys: 0, symmetricKey: 0 })

    }
    async findChatsForUserWithUnReadCount(tenantId: string, userId: string) {
        this.logService.log(' jobProUserId: ', userId, ' tenantId: ', tenantId)
        if (!userId) throw new BadRequestException('JobPro user id is required to load chats');

        const publicChats = await this.chatModel.find({ isPrivate: false, tenantId, chatType: CHAT_TYPE.CIRCLE }, { encryptedKeys: 0, symmetricKey: 0 }).populate({
            path: 'users',
            model: 'ChatUser',
            select: '-privateKey -publicKey'
        }
        ).populate({
            path: 'createdBy',
            model: 'ChatUser',
            select: '-privateKey -publicKey'
        }
        );
        // let generals = publicChats.filter(chat => chat.name.includes('General'));
        // let idsWithCount: Array<{ id: string, count: number }> = [];
        // for (let i = 0; i < generals.length; i++) {
        //     const msgCount = await this.chatMessageService.findTotalMessageCountForAChat(generals[i].id);
        //     idsWithCount.push({ id: generals[i].id, count: msgCount })
        // }
        // idsWithCount.sort((a, b) => b.count - a.count);
        // let maxId = idsWithCount.shift();

        // for (let i = 0; i < idsWithCount.length; i++) {
        //     publicChats.splice(publicChats.findIndex(a => a.id == idsWithCount[i].id), 1)
        // }

        let privateChats = await this.chatModel.find({ $or: [{ tenantId: tenantId, users: userId }, { isGeneral: true, tenantId }] }, { encryptedKeys: 0, symmetricKey: 0 }).populate({
            path: 'users',
            model: 'ChatUser',
            select: '-privateKey -publicKey'
        }
        ).populate({
            path: 'createdBy',
            model: 'ChatUser',
            select: '-privateKey -publicKey'
        }
        );

        let chatUser = await this.chatUserService.findUser(userId);
        if (!chatUser) throw new BadRequestException('User not found!');
        const generalChat = await this.findGeneralChat(tenantId)
        if (!publicChats.length || !generalChat) {
            await this.createChat({
                name: 'General',
                tenantId,
                createdBy: chatUser.id,
                jobProUserIds: [chatUser.jobProUserId],
                users: [chatUser.id],
                chatType: CHAT_TYPE.CIRCLE,
                isPrivate: false,
                isGeneral: true
            });
            await this.createChat({
                name: 'AI Training',
                tenantId,
                createdBy: chatUser.id,
                jobProUserIds: [chatUser.jobProUserId],
                users: [chatUser.id],
                chatType: CHAT_TYPE.CIRCLE,
                isPrivate: true,
                isGeneral: false
            });


            const publicChats = await this.chatModel.find({ isPrivate: false, tenantId, chatType: CHAT_TYPE.CIRCLE }, { encryptedKeys: 0, symmetricKey: 0 }).populate({
                path: 'users',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            }
            ).populate({
                path: 'createdBy',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            }
            );
            let privateChats = await this.chatModel.find({ $or: [{ tenantId: tenantId, users: userId }, { isGeneral: true, tenantId }] }, { encryptedKeys: 0, symmetricKey: 0 }).populate({
                path: 'users',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            }
            ).populate({
                path: 'createdBy',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            }
            );

            return { privateChats, publicChats }
        }

        return { privateChats, publicChats };
    }

    async findGeneralChat(tenantId: string) {
        const chat = await this.chatModel.findOne({ isGeneral: true, name: 'General', tenantId }, { encryptedKeys: 0, symmetricKey: 0 });
        return chat?.toJSON();
    }

    async addMessageToChat(messageId: string, chatId: string) {
        // const chat = await this.findChatById(chatId);
        // if (!chat) throw new NotFoundException('Chat not found');
        // this.chatModel.updateOne({ _id: chatId }, { $push: { messages: new Types.ObjectId(messageId) } });
    }

    /**
     * 
     * @param chatId the id of the chat to update
     * @param creatorId is the user id of the person that created the chat
     * @param data the object with the parameters to update
     */
    async updateChat(chatId: string, creatorId: string, data: UpdateChatDto) {
        if (!chatId) throw new BadRequestException('Chat id and creator id are required')
        const chat = await this.findChatById(chatId);
        // if (chat.createdBy.toString() !== creatorId) throw new BadRequestException('Only the creator of a chat can update a chat');

        return await this.chatModel.updateOne({ _id: chatId }, data);
    }

    async search(name: string, description: string, date: string) {
        const query = {}
        if (name) {
            query['name'] = { $regex: name, $options: 'i' };
        }
        if (description) {
            query['description'] = { $regex: description, $options: 'i' };
        }
        if (date) {
            query['createdAt'] = { $regex: date, $options: 'i' };
        }
        const messages = await this.chatModel.find(query, { symmetricKey: 0, encryptedKeys: 0 });
        return messages;
    }
    encryptWithPublicKey(publicKey: string, symmetricKey: string): string {
        const buffer = Buffer.from(symmetricKey);
        const encrypted = crypto.publicEncrypt(publicKey, buffer);
        return encrypted.toString('base64');
    }
    decryptWithPrivateKey(privateKey: string, encryptedData: string): string {
        const buffer = Buffer.from(encryptedData, 'base64');
        const decrypted = crypto.privateDecrypt(privateKey, buffer);
        return decrypted.toString('utf8');
    }
    async getSymmetricKey(userId: string, chatId: string): Promise<string> {

        const chat = await this.chatModel.findById(chatId);
        let encryptedData = chat.encryptedKeys.find((key) => key.userId.toString() === userId);
        if (!encryptedData) {
            const keys = await this.createSymetricKeyForParticipant();
            encryptedData = chat.encryptedKeys.find((key) => key.userId.toString() === userId);
            this.logService.log('Keys: ', keys);

        }
        this.logService.log('encryptedData: ', encryptedData, 'chatId: ', chatId, 'userId: ', userId)
        try {
            const user = await this.chatUserService.findUser(userId);
            return this.decryptWithPrivateKey(user.privateKey, encryptedData.encryptedKey);

        } catch (error) {
            this.logService.log('Error: ', error)
            const { privateKey } = await this.chatUserService.createKeys(userId);
            // this.logService.log('update keys: ', privateKey)
            return this.decryptWithPrivateKey(privateKey, encryptedData.encryptedKey);
        }
    }


}
