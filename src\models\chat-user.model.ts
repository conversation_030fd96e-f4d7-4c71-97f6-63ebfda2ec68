import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Socket } from "socket.io";
import { ChatDto } from "src/dto/dtos";
import { USER_STATUSES } from "src/messages/dto/event.payload.dto";


@Schema({
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class ChatUser {
    @Prop({ unique: true }) jobProUserId: string
    @Prop() username: string
    @Prop({ default: '#008CFE' }) colorPreference: string;
    @Prop() chats: ChatDto[];
    @Prop() tenantId: string[];
    @Prop({ required: true }) email: string;
    @Prop() publicKey: string;
    @Prop() privateKey: string;
    @Prop() activeChatId: string;
    @Prop({ default: USER_STATUSES.OFFLINE }) status: string;
    @Prop({ default: [] }) sockets: string[];
    id?: string;

}

export const ChatUserSchema = SchemaFactory.createForClass(ChatUser);