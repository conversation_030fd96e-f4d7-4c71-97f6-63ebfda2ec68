/* 5415619.68
* 5114504.28
https://docs.nestjs.com/providers#services
*/

import { BadRequestException, Inject, Injectable, NotFoundException, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ChatMessageDto, ChatMessageHttpDto, ChatUserDto, UpdateChatMessageDto } from 'src/dto/dtos';
import { ChatMessage } from 'src/models/chat-message.model';
import { ChatService } from './chat.service';
import { UpdateMessageDto } from 'src/messages/dto/update-message.dto';
import { FileDto } from 'src/messages/dto/file.dto';
import { ChatUser } from 'src/models/chat-user.model';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { LogService } from 'src/log/log.service';
import { MailgunService } from 'src/mail/services/mailgun.service';
import { ChatInviteService } from './chat-invite.service';
import { groupByField } from 'src/helper.service';
import { encrypt, fieldEncryption, } from 'mongoose-field-encryption';
import { createHash, randomBytes } from 'crypto';
import crypto from 'node:crypto';
import { RabbitMQService } from 'src/rabbitmq/rabbitmq.service';
import { CHAT_TYPE } from 'src/models/chat.model';

@Injectable()
export class ChatMessageService {

    constructor(
        private mq: RabbitMQService,
        private mailgunService: MailgunService,
        private chatUserService: ChatuserService,
        private logService: LogService,
        private chatInviteService: ChatInviteService,
        @InjectModel(ChatMessage.name) private chatMessageModel: Model<ChatMessage>,
        @Inject(forwardRef(() => ChatService)) private chatService: ChatService
    ) {
        // this.findMessageById('66570ff53d7f4ea413374a29')
        // this.getPaginatedMessages('66673a9aded9babe4fc6bb3a', 0, 10)
        // this.updateSenderValue();
        // this.updateMessages();
    }
    async getParentMessage(parentId: string) {
        return await this.findMessageById(parentId);
    }
    async updateMessages() {
        try {
            const messages = await this.chatMessageModel.find({}).populate({
                path: 'sender',
                model: "ChatUser"
            });
            this.logService.log('message: ', messages[0])
            for (const message of messages) {
                // if (!message.senderJobProUserId) {
                message.senderJobProUserId = message.sender ? message.sender['jobProUserId'] : '';
                // this.logService.log('sender jobProUserId: ', message.senderJobProUserId, message?.sender ? message.sender['jobProUserId'] : '');

                await message.save();
                // }
            }
        } catch (error) {
            this.logService.log('Error updating messages: ', error)
        }
    }

    async markAllAsRead(chatId: string, userId: string) {
        try {
            const { modifiedCount } = await this.chatMessageModel.updateMany(
                { chatId, readBy: { $ne: userId } },
                { $addToSet: { readBy: userId } }
            );
            return { success: true, updatedMessages: modifiedCount };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    async getAllUnReadWithLastMessageInChat(chatId: string, userId) {
        // this.logService.log('getLastMessage: ', getLastMessage)
        return [];
        const messages = await this.chatMessageModel.find({ chatId, readBy: { $ne: userId } }).populate({
            path: 'sender',
            model: "ChatUser",

        });
        const last = await this.chatMessageModel.find({ chatId, sender: { $regex: /^(?:ObjectId\()?"[a-f\d]{24}"(?:\))?$/i }, })
            .sort({ createdAt: -1 }).populate({
                path: 'sender',
                model: "ChatUser"
            }).limit(20);

        return Array.from(new Set([...messages, ...last]))

    }

    async getChatAllUnReadMessages(chatId: string, userId: string,) {
        this.logService.log('chatId: ', chatId)
        const messages = await this.chatMessageModel.find({ chatId, readBy: { $ne: userId } }).populate({
            path: 'sender',
            model: "ChatUser"
        });
        return messages;

    }
    async updateSenderValue() {
        const messages = await this.chatMessageModel.find({
            $or: [
                // { sender: { $type: "object" } },
                { sender: { $type: "string" } } // If some are non-ObjectId strings
            ]
        });
        this.logService.log('messages: ', messages, messages.length)

        for (let message of messages) {

            // If sender is a string, ensure it's a valid ObjectId
            const sid = message.sender.toString().replace('"ObjectId(', '').replace(')"', '');
            this.logService.log('sid: ', sid, message.sender)
            if (Types.ObjectId.isValid(message.sender)) { continue; }
            const sender = new Types.ObjectId(sid);
            // const up = await this.chatMessageModel.updateOne({ _id: message.id }, {
            //     sender: new Types.ObjectId(message!.sender!)
            // });
            this.logService.log('sender id: ', sender)
            delete message.sender;
            message['sender'] = sender;
            this.logService.log('update result 1: ', sender);

            const updated = await message.save();
            this.logService.log('updated msg: ', updated)
        }
        this.logService.log('update complete');
    }
    async findAllMessagesForAChat(chatId: string) {
        return await this.chatMessageModel.find({ chatId: chatId });
    }
    async search(message: string, date: string) {
        const messageToSearchWith = new ChatMessage();
        messageToSearchWith.text = message;
        messageToSearchWith.createdAt = new Date(date);
        const secret = process.env.MESSAGE_ENC_SECRET || '!325043439SKLEF232';
        this.logService.log('secret: ', secret);
        const defaultSaltGenerator = (secret) => randomBytes(16);
        const _hash = (secret) => createHash("sha256").update(secret).digest("hex").substring(0, 32);

        const encMessage = encrypt(message, _hash(secret), defaultSaltGenerator)
        this.logService.log('Encrypted text: ', encMessage)
        const query = {}
        if (message) {
            query['text'] = { $regex: encMessage, $options: 'i' };
        }
        if (date) {
            query['createdAt'] = { $regex: date, $options: 'i' };
        }
        const messages = await this.chatMessageModel.find(query);
        return messages;
    }

    async findChatAndCreateMessage(message: ChatMessageHttpDto, sender: ChatUserDto, tenantId: string) {
        const user = await this.chatUserService.findUserByJobProId(message.email);
        if (!user) throw new NotFoundException('User with the specified email not found!');
        let chat = await this.chatService.findDMChatByUserIds(sender.id, user.id, tenantId);
        let chatId = chat.id ?? '';
        if (!chat) {
            const newChat = await this.chatService.createChat({ tenantId, emails: [user.email, sender.email], users: [user.id, sender.id], jobProUserIds: [user.jobProUserId, sender.jobProUserId], chatType: CHAT_TYPE.DM, createdBy: sender.id, name: '', isGeneral: false });
            chatId = newChat.id;
        }
        message.chatId = chatId;
        const savedMessage = await this.createMessage(message as ChatMessageDto);

        return savedMessage;
    }

    async updateMessageTenantId() {
        const messages = await this.chatMessageModel.find();
        for (let i = 0; i < messages.length; i++) {
            const chat = await this.chatService.findChatById(messages[i].chatId);
            await this.chatMessageModel.updateOne({ _id: messages[i].id }, { tenantId: chat.tenantId })
        }
        return await this.chatMessageModel.find()
    }
    async getMentions(user: ChatUser, tenantId: string) {
        const mentionMessages = await this.chatMessageModel.find({ mentions: user.jobProUserId, tenantId })
            .populate({
                path: 'sender', model: 'ChatUser',
                select: '-publicKey -privateKey',
            },
            )
        const grouped = await groupByField(mentionMessages, 'chatId');
        const formatted = []
        const chatIds = Object.keys(grouped)
        for (let i = 0; i < chatIds.length; i++) {
            this.logService.log('chatId: ', chatIds[i])
            const obj = {
                mentions: grouped[chatIds[i]],
                chat: await this.chatService.findChatById(chatIds[i])
            }
            formatted.push(obj)
        }
        return formatted;
    }
    async getReactions(user: ChatUser, tenantId: string) {
        const reactionMessages = await this.chatMessageModel.find({ tenantId, sender: user.id, reactions: { $ne: [] } })
            .populate({
                path: 'sender', model: 'ChatUser',
                select: '-publicKey -privateKey',
            },
            )

        const grouped = await groupByField(reactionMessages, 'chatId');
        this.logService.log('all reactions: ', grouped);
        const formatted = []
        const chatIds = Object.keys(grouped)
        for (let i = 0; i < chatIds.length; i++) {
            this.logService.log('chatId: ', chatIds[i])
            const obj = {
                messagesWithReactions: grouped[chatIds[i]],
                chat: await this.chatService.findChatById(chatIds[i])
            }
            formatted.push(obj)
        }
        return formatted;

    }
    async getInvites(user: ChatUser, tenantId: string) {
        const allUserNotice = await this.chatInviteService.findAllUserNotice(user.jobProUserId);
        const userAddedNotices = allUserNotice.filter(notice => notice.type == 'added');
        const userRemovedNotice = allUserNotice.filter(notice => notice.type == 'removed');
        const userLeftNotice = allUserNotice.filter(notice => notice.type == 'left');
        return { userAddedNotices, userRemovedNotice, userLeftNotice }
    }
    async userActivities(user: ChatUser, tenantId: string) {
        this.logService.log('JobProUserId: ', user.jobProUserId);
        const mentionMessages = await this.chatMessageModel.find({ mentions: user.jobProUserId })
            .populate({
                path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',
            },
            ).populate({
                path: 'chatId', model: 'Chat', select: '-users -jobProUserIds',
            });
        const reactionMessages = await this.chatMessageModel.find({ sender: user.id, reactions: { $ne: [] } })
            .populate({
                path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',
            },
            ).populate({ path: 'chatId', model: 'Chat', select: '-users -jobProUserIds' },);
        const addedToChatMessages = await this.chatMessageModel.find({ addedJobProUserIds: user.jobProUserId })
            .populate({
                path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',

            },
            ).populate({ path: 'chatId', model: 'Chat', select: '-users -jobProUserIds' },);

        const updatedMentionMessages = [];
        for (const activity of mentionMessages) {
            const replies = await this.chatMessageModel.find({ parentId: activity.id })
                .populate({
                    path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',
                })
                .populate({ path: 'chatId', model: 'Chat', select: '-users -jobProUserIds' });

            // this.logService.log('replies: ', replies);
            updatedMentionMessages.push({ ...activity.toJSON(), replies });

        }
        const updatedReactionMessages = [];
        for (const activity of reactionMessages) {
            const replies = await this.chatMessageModel.find({ parentId: activity.id })
                .populate({
                    path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',
                })
                .populate({ path: 'chatId', model: 'Chat', select: '-users -jobProUserIds' });

            // this.logService.log('replies: ', replies);
            updatedReactionMessages.push({ ...activity.toJSON(), replies });

        }
        const updatedAddedToChatMessages = [];
        for (const activity of addedToChatMessages) {
            const replies = await this.chatMessageModel.find({ parentId: activity.id })
                .populate({
                    path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',
                })
                .populate({ path: 'chatId', model: 'Chat', select: '-users -jobProUserIds' });

            // this.logService.log('replies: ', replies);
            updatedAddedToChatMessages.push({ ...activity.toJSON(), replies });

        }

        const allUserNotice = await this.chatInviteService.findAllUserNotice(user.jobProUserId);
        const userAddedNotices = allUserNotice.filter(notice => notice.type == 'added');
        const userRemovedNotice = allUserNotice.filter(notice => notice.type == 'removed');
        const userLeftNotice = allUserNotice.filter(notice => notice.type == 'left');
        // const userRemovedNotices = await this.chatInviteService.findAllUserRemovedNotice(user.jobProUserId);
        return { userAddedNotices, userRemovedNotice, userLeftNotice, mentionMessages: updatedMentionMessages, reactionMessages: updatedReactionMessages }
    }
    async getDialogCount(userId: string, tenantId: string) {
        const user = await this.chatUserService.findUser(userId);
        const dialogCount = await this.chatMessageModel.countDocuments({
            $or: [
                { mentions: user.jobProUserId },
                { mentions: { $ne: [] }, sender: userId, parentId: '' }
            ]
        });
        this.logService.log('dialogCount: ', dialogCount)
        return { dialogCount }
    }
    async userDialogs(userId: string, tenantId: string) {

        const user = await this.chatUserService.findUser(userId);

        this.logService.log('user for dialogs: ', user);

        // Find messages that match the criteria
        const dialogs = await this.chatMessageModel.find({
            $or: [
                { mentions: user.jobProUserId },
                { mentions: { $ne: [] }, sender: userId, parentId: { $exists: false } }
            ]
        });

        // Populate the user, receiverId, and room fields
        await this.chatMessageModel.populate(dialogs, [
            {
                path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey',
            },
            { path: 'chatId', model: 'Chat' },
        ]);
        this.logService.log('dialog 1: ', dialogs)
        // Filter for messages with replies



        const updatedDialogs = [];
        for (const dialog of dialogs) {
            const replies = await this.chatMessageModel.find({ parentId: dialog.id });
            await this.chatMessageModel.populate(replies, [
                {
                    path: 'sender', model: 'ChatUser', select: '-publicKey -privateKey', match: {
                        sender: {
                            $type: ['string', 'objectId']
                        }
                    }
                },
                { path: 'chatId', model: 'Chat' },

            ]);
            this.logService.log('replies: ', replies);
            updatedDialogs.push({ ...dialog.toJSON(), replies });

        }

        // Return the updated dialogs
        this.logService.log('updated dialogs: ', updatedDialogs);
        return updatedDialogs;


    }

    async updateCallMessage(callId: string, data: { callStatus: string; }) {
        const message = await this.chatMessageModel.findOne({ callId: callId });
        const duration = Date.now() - new Date(message.createdAt).getTime();
        await this.chatMessageModel.updateOne({ callId: callId }, { ...data, callDuration: duration });
        const updatedMessage = await this.findMessageById(message.id);
        this.logService.log('updated chat message: ', updatedMessage);
        return updatedMessage;
    }
    async getChatFilesForUser(chatId: string, userId: string) {
        this.logService.log('chatId: ', chatId, ' senderId: ', userId)
        let messages = await this.chatMessageModel.find({
            chatId: chatId,
            sender: userId,
            files: { $ne: [] }
        },);
        if (messages.length) return messages;

        return await this.chatMessageModel.find({
            chatId: chatId,
            sender: new Types.ObjectId(userId),
            files: { $ne: [] }
        },);
        let found = await this.chatMessageModel.aggregate([
            {
                $match: {
                    chatId: chatId,
                    sender: userId,
                    files: { $ne: [] }
                },
            },
            {
                $group: {
                    _id: null,
                    mergedFiles: { $push: '$files' },
                },
            },
            {
                $project: {
                    _id: 0,
                    mergedFiles: {
                        $reduce: {
                            input: '$mergedFiles',
                            initialValue: [],
                            in: { $concatArrays: ['$$value', '$$this'] },
                        },
                    },
                },
            },
        ]);
        if (found.length) return found;
        this.logService.log('files not found... get with user object id')
        return await this.chatMessageModel.aggregate([
            {
                $match: {
                    chatId: chatId,
                    sender: new Types.ObjectId(userId),
                    files: { $ne: [] }
                },
            },
            {
                $group: {
                    _id: null,
                    mergedFiles: { $push: '$files' },
                },
            },
            {
                $project: {
                    _id: 0,
                    mergedFiles: {
                        $reduce: {
                            input: '$mergedFiles',
                            initialValue: [],
                            in: { $concatArrays: ['$$value', '$$this'] },
                        },
                    },
                },
            },
        ]);
    }
    async getChatFiles(chatId: string): Promise<FileDto[]> {
        return await this.chatMessageModel.find({
            chatId: chatId,
            files: { $ne: [] }
        },);
        return await this.chatMessageModel.aggregate([
            {
                $match: {
                    chatId: chatId,
                    files: { $ne: [] }
                },
            },
            {
                $group: {
                    _id: null,
                    mergedFiles: { $push: '$files' },
                },
            },
            {
                $project: {
                    _id: 0,
                    mergedFiles: {
                        $reduce: {
                            input: '$mergedFiles',
                            initialValue: [],
                            in: { $concatArrays: ['$$value', '$$this'] },
                        },
                    },
                },
            },
        ]);
    }

    async findMessageById(messageId: string) {
        if (!messageId) throw new BadRequestException('Message id is required');
        const found = await this.chatMessageModel.findOne({ _id: messageId }).populate({
            model: 'ChatUser',
            path: 'sender',
            select: '-publicKey -privateKey',

        })


        if (!found) throw new NotFoundException('Message not found!');
        this.logService.log('found message sender: ', found.sender);
        // const sender = await this.chatUserService.findUser(found.sender);
        // this.logService.log('sender: ', sender)
        return found;
    }
    async createCallMessage(data: ChatMessageDto) {
        if (data.callId) {
            const found = await this.chatMessageModel.findOne({ callId: data.callId });
            if (found) {
                return { alreadyExist: true, ...found }
            } else {
                const chat = await this.chatService.findChatById(data.chatId.toString());
                console.log('chat: ', chat);
                // const symmetricKey = await this.chatService.getSymmetricKey(data.sender.id, data.chatId);
                // const user = await this.chatUserService.findUser(data.sender.id);
                const message = await this.chatMessageModel.create({ ...data, sender: data.sender, tenantId: chat.tenantId, });

                return await this.findMessageById(message.id);
            }
        }
    }
    async createMessage(data: ChatMessageDto) {
        this.logService.log('creating new message: ', data)
        console.log('data: ', data);
        // data.sender = data.sender['id'];

        const chat = await this.chatService.findChatById(data.chatId.toString());

        // const symmetricKey = await this.chatService.getSymmetricKey(data.sender.id, data.chatId);
        let sender = null;
        if (typeof data.sender == 'object') {
            sender = new Types.ObjectId(data.sender!['id'] || data.sender!['_id']);
        } else {
            sender = new Types.ObjectId(data.sender);
        }
  
        const user = await this.chatUserService.findUser(data.sender.toString());
        if (!user) throw new BadRequestException('Invalid user data for sender')
        console.log('sender: ', sender, ' user: ', user);
        const message = await this.chatMessageModel.create({ ...data, sender: sender, tenantId: chat.tenantId ?? '', });

        console.log('message created: ', message);
        return await this.findMessageById(message.id);
    }

    async updateMessage(messageId: string, data: UpdateChatMessageDto) {
        await this.findMessageById(messageId);
        await this.chatMessageModel.updateOne({ _id: messageId }, { ...data, isEdited: true, __enc_text: false });
        return await this.findMessageById(messageId);
    }

    async updateReadStatus(messageId: string, userId: string) {
        const message = await this.findMessageById(messageId);
        const up = await this.chatMessageModel.updateOne({ _id: messageId, readBy: { $ne: userId } }, { $push: { readBy: userId } })
        this.logService.log('updated red status: ', up);
        message.readBy.push(userId);

        return await message.populate({
            path: 'sender',
            model: 'ChatUser',
            select: '-privateKey -publicKey'
        });
    }

    async getAllUnReadMessageCount(userId: string, tenantId: string) {
        const userChatIds = (await this.chatService.findChatsForUser(userId, tenantId)).map(chat => chat.id);
        const count = await this.chatMessageModel.countDocuments({ chatId: { $in: userChatIds }, readBy: { $ne: userId } });
        return count;
    }

    async getAllUnReadMessageCountPerChat(userId: string, tenantId: string, chatId: string) {

        const count = await this.chatMessageModel.countDocuments({ chatId: chatId, readBy: { $ne: userId } });
        return count;
    }

    async deleteMessage(messageId: string) {
        if (!messageId) throw new BadRequestException('Message id is required');
        return (await this.chatMessageModel.findOneAndUpdate({ _id: messageId }, { isDeleted: true }, { new: true }))
            .populate({
                path: 'sender',
                model: 'ChatUser',
                select: '-privateKey -publicKey'
            })
    }
    async pinAndUnPinMessage(userId: string, messageId: string, isPinned: boolean) {
        if (!userId || !messageId) throw new BadRequestException('user id and message id are required');
        const updateRes = await this.chatMessageModel.updateOne({ _id: messageId }, { pinnedBy: userId, isPinned });
        return await this.findMessageById(messageId);
    }
    async getPaginatedMessages(chatId: string, page?: number, pageSize?: number) {

        page = page ?? 0;
        pageSize = pageSize ?? 20;
        if (!chatId) throw new BadRequestException('chat id is required');

        const messages = await this.chatMessageModel
            .find({ chatId })
            .sort({ createdAt: -1 })
            .skip((page) * (pageSize))
            .limit(pageSize).populate({
                path: 'sender',
                model: 'ChatUser',
                select: '-privateKey -publicKey',

            })
        const totalMessage = await this.chatMessageModel.countDocuments({ chatId })
        // this.logService.log('paginated messages: ', messages, totalMessage);
        return { loadedMessages: messages, messageCount: totalMessage };

        // const symmetricKey = await this.chatService.getSymmetricKey(userId, chatId);
        // this.logService.log('Symetric: ', symmetricKey)
        // return messages.map((message) => ({
        //     ...message.toObject(),
        //     text: message.e2e ? this.decryptWithPrivateKey(user.privateKey, message.text) : message.text,
        // }));
    }

    async getLastMessageForAChat(chatId: string) {
        this.logService.log('chat id for last message: ', chatId)
        const lastMessage = await this.chatMessageModel
            .findOne({ chatId: chatId.toString() })
            .sort({ createdAt: -1 })
            .exec();
        return lastMessage;
    }
    async addReaction(userId: string, jobProUserId: string, reaction: string, messageId: string) {
        if (!userId || !reaction || !messageId) throw new BadRequestException(' user id, reaction and message id are required');
        const reactionobj = { userId, jobProUserId, reaction };
        await this.chatMessageModel.updateOne({ _id: messageId, reactions: { $ne: reaction } },
            { $push: { reactions: reactionobj } });

        return await this.findMessageById(messageId);
    }

    async removeReaction(userId: string, jobProUserId: string, reaction: string, messageId: string) {
        if (!userId || !reaction || !messageId || !jobProUserId) throw new BadRequestException(' user id, jobProUserId, reaction and message id are required');
        const reactionobj = { userId, jobProUserId, reaction };
        await this.chatMessageModel.updateOne({ _id: messageId, reactions: reactionobj },
            { $pull: { reactions: reactionobj } });
        return this.findMessageById(messageId)
    }
    async findTotalMessageCountForAChat(chatId: string) {
        const messageCount = await this.chatMessageModel.countDocuments({ chatId });
        this.logService.log('message count: ', chatId, messageCount)
        return messageCount;
    }
    isBase64(str) {
        try {
            return btoa(atob(str)) === str;
        } catch (err) {
            return false;
        }
    }
    encryptWithPublicKey(publicKey: string, symmetricKey: string): string {
        const buffer = Buffer.from(symmetricKey);
        const encrypted = crypto.publicEncrypt(publicKey, buffer);
        return encrypted.toString('base64');
    }
    decryptWithPrivateKey(privateKey: string, encryptedData: string): string {
        try {
            if (!this.isBase64(encryptedData)) {
                this.logService.log('not a base64 string')
                return encryptedData;
            }

            const buffer = Buffer.from(encryptedData, 'base64');
            const decrypted = crypto.privateDecrypt(
                {
                    key: privateKey,
                    padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                    oaepHash: "sha256",
                },
                buffer
            );
            return decrypted.toString('utf8');
        } catch (error) {
            this.logService.error('Decryption error:', error);
            throw error;
        }
    }
    // encryptWithSymmetricKey(key: string, data: string): string {
    //     const cipher = crypto.createCipher('aes-256-cbc', Buffer.from(key, 'hex'));
    //     let encrypted = cipher.update(data, 'utf8', 'hex');
    //     encrypted += cipher.final('hex');
    //     return encrypted;
    //   }
    //   decryptWithSymmetricKey(key: string, encryptedData: string): string {
    //     const decipher = crypto.createDecipher('aes-256-cbc', Buffer.from(key, 'hex'));
    //     let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    //     decrypted += decipher.final('utf8');
    //     return decrypted;
    //   }
}

