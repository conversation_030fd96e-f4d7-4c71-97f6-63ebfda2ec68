/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { ChatService } from './chat.service';
import { ChatMessageService } from './chat-message.service';
import { ActivityService } from './activity.service';
import { ChatUserDto } from 'src/dto/dtos';

@Injectable()
export class SearchService {

  constructor(private userService: ChatuserService, private chatService: ChatService, private messageService: ChatMessageService, private activityService: ActivityService) { }
  async search(name: string, message: string, date: string, description: string,user:ChatUserDto) {
    const users = await this.userService.search(name, date,);
    const messages = await this.messageService.search(message, date);
    const chats = await this.chatService.search(name, description, date,);
    const activities = await this.activityService.search(message, date,user);
    return {
      users,
      messages,
      chats,
      activities
    }
  }
}
