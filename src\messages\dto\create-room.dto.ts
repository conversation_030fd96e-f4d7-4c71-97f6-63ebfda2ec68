import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsObject, IsString } from 'class-validator';
import { CreateUserDto } from 'src/user/dto/create-user.dto';

export class CreateRoomDto {
  @ApiProperty()
  @IsString()

  name: string;
  @ApiProperty()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsObject()
  users?: CreateUserDto[];

  @ApiProperty()
  @IsDate()
  createdAt: Date;

  @ApiProperty()
  @IsDate()
  updatedAt: Date;
}
