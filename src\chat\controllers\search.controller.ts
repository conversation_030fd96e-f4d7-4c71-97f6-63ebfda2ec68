/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Get, Query } from '@nestjs/common';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { ActivityService } from '../services/activity.service';
import { ChatService } from '../services/chat.service';
import { ChatMessageService } from '../services/chat-message.service';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { GetUser } from 'src/get-user.decorator';
import { ChatUserDto } from 'src/dto/dtos';
import { SearchService } from '../services/search.service';
import { LogService } from 'src/log/log.service';

@ApiTags('search')
@Controller('search')
export class SearchController {
  constructor(
    private activityService: ActivityService,
    private chatService: ChatService,
    private messageService: ChatMessageService,
    private userService: ChatuserService,
    private searchService: SearchService,
    private logService: LogService) { }


  @Get()
  @ApiQuery({ name: 'name', required: false }) // Specify that 'name' query parameter is optional
  @ApiQuery({ name: 'message', required: false }) // Specify that 'message' query parameter is optional
  @ApiQuery({ name: 'model', required: false }) // Specify that 'model' query parameter is optional
  @ApiQuery({ name: 'date', required: false }) // Specify that 'date' query parameter is optional

  search(@GetUser() user: ChatUserDto,
    @Query('name') name?: string,
    @Query('message') message?: string,
    @Query('model') model?: Models,
    @Query('date') date?: string,
    @Query('description') description?: string
  ) {
    this.logService.log('name: ', name, ' message: ', message, 'model: ', model);

    if (model) {
      switch (model) {
        case Models.ACTIVITY:
          this.logService.log('querying activity: ', message, date)
          return this.activityService.search(message, date, user);
        case Models.MESSAGE:
          this.logService.log('querying message: ', message, date)
          return this.messageService.search(message, date);
        case Models.USER:
          this.logService.log('querying user: ', message, date)
          return this.userService.search(name, date);
        case Models.CHAT:
          this.logService.log('querying chat: ', name, date)
          return this.chatService.search(name, description, date);
        default:
          break;
      }
    }

    // If model is not specified or invalid, fallback to the generic search service
    return this.searchService.search(name, message, date, description, user);
  }
}



const enum Models {
  USER = 'user',
  ACTIVITY = 'activity',
  MESSAGE = 'message',
  CHAT = "chat"
}