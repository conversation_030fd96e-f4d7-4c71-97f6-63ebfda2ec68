import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { SecuritySchemeObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import * as bodyParser from 'body-parser';
import { AppModule } from './app.module';
// import { ClusteringService } from './clutering.service';
const crypto = require('crypto');
// import { RedisIoAdapter } from './chat/redis.adapter'; 
import { ClusteringService } from './clutering.service';
import { RedisIoStreamAdapter } from './chat/redis-stream.adapter';
import { RedisService } from './chat/redis.service';

// import { IoAdapter } from '@nestjs/platform-socket.io';
// import { RedisIoAdapter } from './chat/redis.adapter';

const randomBytes = crypto.randomBytes(32).toString('hex');

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule
  );
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection:', reason);
  });

  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    // Optional: Shut down the process gracefully
    // process.exit(1);
  });

  app.useGlobalPipes(new ValidationPipe());
  app.setGlobalPrefix('/api/');
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
  });
  const scheme: SecuritySchemeObject = {
    scheme: 'jwt', type: 'apiKey'
  }
  const config = new DocumentBuilder()
    .setTitle('Chat Service API')
    .setDescription('The chat service API documentation')
    .setVersion('1.0')
    .addTag('Chat')
    .addBearerAuth(scheme)
    .build();

  const document = SwaggerModule.createDocument(app, config);

  SwaggerModule.setup('swagger/index.html', app, document);

  app.use(bodyParser.json({ limit: '200mb' }));
  app.use(bodyParser.urlencoded({ extended: true, limit: '200mb' }));

  const apiGateway = process.env.NODE_ENV == 'production' ? 'api.jobpro.app' : 'api.pactocoin.com';

  // Set how long the message should live on the queue i.e ttl
  const expiration = 360000000; // 100 hours
  const queueArgs = {
    'x-message-ttl': expiration
  };

  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [`amqps://xruwodqv:<EMAIL>/xruwodqv`],
      // queue: QUEUE,
      queueOptions: {
        durable: true,
        arguments: queueArgs
      }
    }
  })
  const redisIoAdapter = new RedisIoStreamAdapter(app);
  await redisIoAdapter.connectToRedis();

  app.useWebSocketAdapter(redisIoAdapter);

  app.startAllMicroservices()
  await app.listen(process.env.PORT ? parseInt(process.env.PORT) : 9000);
}
// bootstrap()
ClusteringService.startClusteredApp(bootstrap);

