import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Room } from 'src/messages/models/room.model';
import { UserDto } from '../user/dto/user.dto';

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret.__v;
      ret.id = ret._id;
      delete ret._id;
    },
  },
  toObject: { virtuals: true },
})
export class User extends Document {
  @Prop() subdomain: string;
  @Prop() username: string;
  @Prop() email: string;
  @Prop() phone: string;
  @Prop() password: string;
  @Prop({ unique: true }) user_id: string;
  @Prop({ default: '#008CFE' }) colorPreference: string;
  @Prop({ type: [Types.ObjectId], ref: 'Room' }) rooms?: Room[];
  @Prop({ type: [Types.ObjectId], ref: 'User' }) connections: UserDto[];
  @Prop() profilePictureUrl: string;
  @Prop() companyId: string;
  @Prop() tenantId: string;
  @Prop() companyName: string;
}
export const UserSchema = SchemaFactory.createForClass(User);
