import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
// import { User } from 'src/user/models/user.model';
import { UserDto } from 'src/user/dto/user.dto';
import { MessageDto } from '../dto/message.dto';
import { Types } from 'mongoose';


@Schema({
  timestamps: true, toObject: { virtuals: true }, toJSON: {
    transform: (doc, ret) => {
      delete ret.__v;
      ret.id = ret._id;
      delete ret._id;
    },
  },
})
export class Room {
  @Prop() name: string;
  @Prop({ type: Types.ObjectId, ref: 'User' }) createdBy: string;
  @Prop() description?: string;
  @Prop() isArchived?: boolean;
  @Prop() users?: string[];
  @Prop() tenantId: string;
  @Prop() createdAt: Date;
  @Prop() updatedAt: Date;
  @Prop() makeChannelPrivate: boolean;
  @Prop() messages?: MessageDto[];
  @Prop() archivedBy?: string;
  @Prop() subdomain: string;
}


export const RoomSchema = SchemaFactory.createForClass(Room)