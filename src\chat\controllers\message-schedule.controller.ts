/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { MessageScheduleService } from '../services/message-schedule.service';
import { ScheduledMessageDto, UpdateMessageScheduleDto } from 'src/call/dto/schedule_message.dto';
import { GetUser } from 'src/get-user.decorator';
import { ChatUser } from 'src/models/chat-user.model';
import { JwtAuthGuard } from 'src/user/jwt.guard';
import { ApiTags } from '@nestjs/swagger';
import { LogService } from 'src/log/log.service';

@ApiTags('Message Schedule')
@UseGuards(JwtAuthGuard)
@Controller('message-schedule')
export class MessageScheduleController {
    constructor(private messageScheduleService: MessageScheduleService, private logService: LogService) { }
    @Get()
    getUserSchedules(@GetUser() user: ChatU<PERSON>) {
        return this.messageScheduleService.getUserSchedules(user);
    }

    @Post()
    scheduleMessage(@Body() messageSchedule: ScheduledMessageDto, @GetUser() user: ChatUser) {
        this.logService.log('foundUser: ', user)
        return this.messageScheduleService.scheduleMessage(messageSchedule, user);
    }

    @Patch('edit/:id')
    editScheduledMessage(@Param('id') id: string, @Body() messageSchedule: UpdateMessageScheduleDto) {
        return this.messageScheduleService.editScheduledMessage(id, messageSchedule)
    }
    @Delete('/:id')
    deleteScheduledMessag(@Param('id') id: string) {
        return this.messageScheduleService.deleteSchedule(id)
    }
    @Get('/get-by-id/:id')
    getById(@Param('id') id: string) {
        return this.messageScheduleService.findById(id)
    }
}
