import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Connection, createConnection } from 'mongoose'
import { LogService } from './log/log.service';

@Injectable()
export class AppService {
  server1Connection: Connection;
  server2Connection: Connection;



  constructor(private configService: ConfigService, private logService: LogService) {
    this.server1Connection = createConnection(this.configService.get('MONGODB_URI1'));
    this.server2Connection = createConnection(this.configService.get('MONGODB_URI'));

    // setTimeout(() => {
    //   this.migrateData()
    // }, 5000);
  }
  getHello(): string {
    return 'Hello World!';
  }


  async migrateData() {
    try {
      // Fetch collections from Server 1
      const collections = await this.server1Connection.db.listCollections().toArray();

      for (const collection of collections) {
        const colName = collection.name;
        const sourceCol = this.server1Connection.collection(colName);
        const targetCol = this.server2Connection.collection(colName);

        this.logService.log(`Migrating collection: ${colName}`);

        // Fetch all documents from Server 1
        const docs = await sourceCol.find({}).toArray();

        if (docs.length > 0) {
          try {
            // Insert all documents into Server 2, preserving the original _id values
            const insertResult = await targetCol.insertMany(docs, { ordered: false });
            // 'ordered: false' will allow the insertion to continue if there are duplicate keys
            this.logService.log(`Inserted ${insertResult.insertedCount} documents into ${colName}`);
          } catch (insertError) {
            this.logService.error(`Error inserting documents into ${colName}:`, insertError);
          }
        } else {
          this.logService.log(`No documents to migrate for collection: ${colName}`);
        }
      }

      this.logService.log('Migration completed successfully');
      return { message: 'Migration completed successfully' };
    } catch (error) {
      this.logService.error('Error during migration:', error.message);
      return { message: 'Migration completed with errors' };
    }
  }




}
