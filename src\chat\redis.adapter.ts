
import { IoAdapter, } from '@nestjs/platform-socket.io';
import { ServerOptions } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import { LogService } from 'src/log/log.service';

export const pubClient = createClient({ url: `redis://redis-11854.c239.us-east-1-2.ec2.redns.redis-cloud.com:11854`, password: process.env.REDIS_PASSWORD || 'hgIRV9RTvmy8qcKVo5t4G6QKPVX8xsjs' });

export const subClient = createClient({ url: `redis://redis-11854.c239.us-east-1-2.ec2.redns.redis-cloud.com:11854`, password: process.env.REDIS_PASSWORD || 'hgIRV9RTvmy8qcKVo5t4G6QKPVX8xsjs' });


export class RedisIoAdapter extends IoAdapter {
    private logService = new LogService();
    private adapterConstructor: ReturnType<typeof createAdapter>;

    async connectToRedis(): Promise<void> {
        try {
            // const subClient = pubClient.duplicate();

            await Promise.all([pubClient.connect(), subClient.connect()]);

            this.adapterConstructor = createAdapter(pubClient, subClient);

            pubClient.on('error', (error) => {
                this.logService.error(`Redis Adapter PubClient Error: ${error}`);
            });

            subClient.on('error', (error) => {
                this.logService.error(`Redis Adapter SubClient Error: ${error}`);
            });
            pubClient.on('connect', (con) => {
                this.logService.error(`Redis Adapter connected: ${con}`);
            });

            subClient.on('connect', (con) => {
                this.logService.error(`Redis Adapter SubClient Error: ${con}`);
            });
        } catch (error) {
            this.logService.log('Error connecting to redis ');
        }
    }
    createIOServer(port: number, options?: ServerOptions): any {
        const server = super.createIOServer(port, options);
        server.adapter(this.adapterConstructor);
        return server;
    }
}