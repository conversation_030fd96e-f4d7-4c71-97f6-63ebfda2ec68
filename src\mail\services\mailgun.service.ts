import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import mailgunTransport, { MailOptions } from 'nodemailer-mailgun-transport';
import { LogService } from 'src/log/log.service';
import { EmailTemplateService, EmailTemplateType } from './email-template.service';

@Injectable()
export class MailgunService {
    private MAILGUN_KEY = this.config.get<string>('MAILGUN_KEY');
    private MAILGUN_DOMAIN = this.config.get<string>('MAILGUN_DOMAIN');
    private clientUrl;
    private readonly transporter: nodemailer.Transporter;
    constructor(
        private readonly config: ConfigService,
        private logService: LogService,
        private emailTemplateService: EmailTemplateService
    ) {
        try {
            // this.logService.log('mailgun key: ', this.MAILGUN_KEY);
            this.transporter = nodemailer.createTransport(mailgunTransport(this.auth));
            this.clientUrl = this.config.get('CLIENT_BASE_URL');

        } catch (error) {
            this.logService.log('Error creating nodemailer transport: ', error)
        }
    }
    auth = {
        auth: {
            api_key: this.MAILGUN_KEY,
            domain: this.MAILGUN_DOMAIN
        },
        defaults: {
            from: '<EMAIL>'
        },
    }


    /**
     * Send via API
     *
     * @param data 
     */
    async sendMail(data: MailOptions) {
        try {
            const mailOptions: nodemailer.SendMailOptions = { ...data, from: '<EMAIL>' };
            this.logService.log('sending email with params: ', data)
            this.transporter.sendMail(mailOptions).then((data: any) => {
                this.logService.log('email response: ', data);
            });
        } catch (error) {
            this.logService.log('Error sending email: ', error)
        }
    }

    /**
     * Send mention email using template
     * @param mentionerName Name of the person who mentioned the user
     * @param userName Name of the mentioned user
     * @param circleName Name of the circle/chat
     * @param messageText The message text containing the mention
     * @param workspace Workspace name for URL generation
     * @param chatId Chat ID for URL generation
     * @param userEmail Email address to send to
     */
    async sendMentionEmail(
        mentionerName: string,
        userName: string,
        circleName: string,
        messageText: string,
        workspace: string,
        chatId: string,
        userEmail: string
    ): Promise<void> {
        try {
            const actionUrl = `https://${workspace}.joble.${this.clientUrl}/suite/pkg/jobchat/${chatId}`;
            const html = await this.emailTemplateService.getMentionTemplate(
                mentionerName,
                userName,
                circleName,
                messageText,
                actionUrl
            );

            await this.sendMail({
                to: [userEmail],
                html,
                subject: 'New Mention in JobChat',
                sender: 'Joble'
            });
        } catch (error) {
            this.logService.error('Error sending mention email: ', error);
            throw error;
        }
    }

    /**
     * Send added to circle email using template
     * @param userName Name of the user being added
     * @param circleName Name of the circle
     * @param inviterName Name of the person who added the user
     * @param workspace Workspace name for URL generation
     * @param chatId Chat ID for URL generation
     * @param userEmail Email address to send to
     */
    async sendAddedToCircleEmail(
        userName: string,
        circleName: string,
        inviterName: string,
        workspace: string,
        chatId: string,
        userEmail: string
    ): Promise<void> {
        try {
            const actionUrl = `https://${workspace}.joble.${this.clientUrl}/suite/pkg/jobchat/${chatId}`;
            const html = await this.emailTemplateService.getAddedToCircleTemplate(
                userName,
                circleName,
                inviterName,
                actionUrl
            );

            await this.sendMail({
                to: [userEmail],
                html,
                subject: `You have been added to ${circleName} Chat on Joble`,
                sender: 'Joble'
            });
        } catch (error) {
            this.logService.error('Error sending added to circle email: ', error);
            throw error;
        }
    }

    /**
     * @deprecated Use sendMentionEmail instead
     */
    mentionHTML(workspace: string, circle: string, chatId: string, message = 'You have a new mention in')
    {
        const html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>New Mention - ${workspace}</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                    line-height: 1.6;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    padding: 20px;
                }
                
                .email-container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: #ffffff;
                    border-radius: 16px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                    overflow: hidden;
                    position: relative;
                }
                
                .header {
                    background: linear-gradient(135deg, #008CFE 0%, #0066CC 100%);
                    padding: 40px 30px;
                    text-align: center;
                    position: relative;
                }
                
                .header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="0.5" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                    opacity: 0.3;
                }
                
                .logo {
                    position: relative;
                    z-index: 1;
                    max-width: 180px;
                    height: auto;
                    margin-bottom: 20px;
                    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
                }
                
                .content {
                    padding: 40px 30px;
                    text-align: center;
                }
                
                .mention-icon {
                    width: 60px;
                    height: 60px;
                    background: linear-gradient(135deg, #FFD700, #FFA500);
                    border-radius: 50%;
                    margin: 0 auto 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 28px;
                    box-shadow: 0 8px 16px rgba(255, 165, 0, 0.3);
                }
                
                .message-text {
                    font-size: 20px;
                    color: #333;
                    margin-bottom: 30px;
                    font-weight: 500;
                }
                
                .workspace-info {
                    margin-bottom: 30px;
                }
                
                .workspace-name {
                    font-size: 36px;
                    font-weight: 700;
                    color: #2c3e50;
                    margin-bottom: 8px;
                    text-transform: capitalize;
                }
                
                .app-name {
                    font-size: 18px;
                    color: #7f8c8d;
                    font-weight: 400;
                }
                
                .circle-info {
                    background: #f8f9fa;
                    border-radius: 12px;
                    padding: 20px;
                    margin: 30px 0;
                    border-left: 4px solid #008CFE;
                }
                
                .circle-label {
                    font-size: 16px;
                    color: #666;
                    margin-bottom: 8px;
                }
                
                .circle-name {
                    font-size: 24px;
                    font-weight: 600;
                    color: #008CFE;
                }
                
                .cta-button {
                    display: inline-block;
                    background: linear-gradient(135deg, #008CFE 0%, #0066CC 100%);
                    color: white !important;
                    text-decoration: none;
                    padding: 16px 32px;
                    border-radius: 50px;
                    font-size: 18px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    box-shadow: 0 8px 20px rgba(0, 140, 254, 0.3);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                
                .cta-button:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 12px 24px rgba(0, 140, 254, 0.4);
                }
                
                .footer {
                    background: #f8f9fa;
                    padding: 20px 30px;
                    text-align: center;
                    border-top: 1px solid #e9ecef;
                }
                
                .footer-text {
                    font-size: 14px;
                    color: #6c757d;
                }
                
                /* Mobile Responsive */
                @media (max-width: 600px) {
                    body {
                        padding: 10px;
                    }
                    
                    .email-container {
                        border-radius: 12px;
                        margin: 10px;
                    }
                    
                    .header {
                        padding: 30px 20px;
                    }
                    
                    .content {
                        padding: 30px 20px;
                    }
                    
                    .logo {
                        max-width: 150px;
                    }
                    
                    .workspace-name {
                        font-size: 28px;
                    }
                    
                    .message-text {
                        font-size: 18px;
                    }
                    
                    .circle-name {
                        font-size: 20px;
                    }
                    
                    .cta-button {
                        padding: 14px 28px;
                        font-size: 16px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <img src="https://chats.pactocoin.com/public/images/Jop%20pro%402x-8.png" 
                        alt="JobPro Logo" class="logo">
                </div>
                
                <div class="content">
                    <div class="mention-icon">@</div>
                    
                    <div class="message-text">${message}</div>
                    
                    <div class="workspace-info">
                        <div class="workspace-name">${workspace}</div>
                        <div class="app-name">JobChat</div>
                    </div>
                    
                    <div class="circle-info">
                        <div class="circle-label">From channel</div>
                        <div class="circle-name">#${circle}</div>
                    </div>
                    
                    <a href="https://${workspace}.joble.${this.clientUrl}/suite/pkg/jobchat/${chatId}" 
                    class="cta-button">
                        View Message
                    </a>
                </div>
                
                <div class="footer">
                    <div class="footer-text">
                        You're receiving this because you were mentioned in ${workspace}
                    </div>
                </div>
            </div>
        </body>
        </html>`;
        
        return html;
    }
}