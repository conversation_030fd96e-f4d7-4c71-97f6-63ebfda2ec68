import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import { ColorPreferenceDto } from '../../dto/color_preference.dto';
import { CreateUserDto } from '../../dto/create-user.dto';
import { UpdateUserDto } from '../../dto/update-user.dto';
import { UserService } from '../../services/v1/user.service';
import { Payload, MessagePattern, Ctx, RmqContext } from '@nestjs/microservices'
import { LogService } from 'src/log/log.service';



@Controller('v1/user')
export class UserController {
  constructor(private readonly userService: UserService, private logService: LogService) { }


  @Get()
  findAll(@Query('tenantId') tenantId?: string,) {
    // this.logService.log('req: ', tenantId);
    return this.userService.findAll(tenantId);
  }

  @Post('')

  async create(@Body() createUserDto: CreateUserDto) {
    this.logService.log('reaching user route');
    const user = await this.userService.create(createUserDto);
    return user;
  }
  @Get('/:id')
  findOne(@Param('id') id: string) {
    this.logService.log('find one in controller: ', id);

    return this.userService.findOne(id);
  }
  @Get('connection/:id')
  findMyConnection(@Param('id') id: string) {
    this.logService.log('find one in controller: ', id);

    return this.userService.findMyConnection(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(id, updateUserDto);
  }

  @Get('findbyuserId/:user_id')
  findByUserId(@Param('user_id') user_id: string) {
    this.logService.log('user_id get: ', user_id);
    return this.userService.findByUserId(user_id);
  }
  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.userService.delete(+id);
  }

  @Put('update-color-preference')
  updateColorPreference(@Body() data: ColorPreferenceDto) {
    this.logService.log('data: ', data);

    return this.userService.updateColorPreference(data);
  }
  @Post('start-chat')
  startChat(@Body() data: any) {
    this.logService.log('starting chat: ', data);
    return this.userService.startChat(data);
  }

  // @MessagePattern('user-created-event')
  // async userCreated(@Payload() data: number[], @Ctx() context: RmqContext) {
  //   this.logService.log(`Pattern: ${context.getPattern()}`);
  //   const message = context.getMessage();
  //   const channelRef = context.getChannelRef();
  //   const content = JSON.parse(message['content'].toString())
  //   const userData = content['data']
  //   this.logService.log('message from RMQ: ', content);
  //   const createUserDto = new CreateUserDto();
  //   createUserDto.email = userData['Email'];
  //   createUserDto.companyId = userData['CompanyId']
  //   createUserDto.user_id = userData['Id']
  //   createUserDto.username = userData['First Name'] + ' ' + userData['LastName'];

  //   await this.userService.create(createUserDto)

  // }
}
