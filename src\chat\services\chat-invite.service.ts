/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatInviteDto } from 'src/call/dto/chat-invite.dto';
import { ChatInvite } from 'src/call/model/chat-invite';

@Injectable()
export class ChatInviteService {
    constructor(
        @InjectModel(ChatInvite.name) private readonly chatInviteModel: Model<ChatInvite>
    ) { }
    async create(invite: ChatInviteDto) {
        return (await this.chatInviteModel.create(invite)).toJSON();
    }
    async findAllUserAddedNotice(jobProUserId: string) {
        return await this.chatInviteModel.find({ jobProUserId, type: 'added' })
    }
    async findAllUserRemovedNotice(jobProUserId: string) {
        return await this.chatInviteModel.find({ jobProUserId, type: 'removed' })
    }
    async findAllUserLeftNotice(jobProUserId: string) {
        return await this.chatInviteModel.find({ jobProUserId, type: 'left' })
    }
    async findAllUserNotice(jobProUserId: string) {
        return await this.chatInviteModel.find({ jobProUserId })
    }
}
