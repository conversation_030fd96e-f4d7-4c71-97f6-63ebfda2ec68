import { ActivityLogService } from './activity-log.service';
/*
https://docs.nestjs.com/modules
*/
import { HttpModule, } from '@nestjs/axios';
import { Module, } from '@nestjs/common';
import { LogModule } from 'src/log/log.module';
import { UserModule } from 'src/user/user.module';


@Module({
    imports: [HttpModule, UserModule, LogModule],
    controllers: [],
    providers: [ActivityLogService,],
    exports: [ActivityLogService]
})
export class ActivityLogModule { }
