import { Prop, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { MessageDto } from "src/messages/dto/message.dto";

export class EncryptedKey {
    userId: string;
    encryptedKey: string
}

@Schema({
    timestamps: true,
    toObject: { virtuals: true },
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
})
export class Chat {
    @Prop() name: string;
    @Prop({ type: Types.ObjectId, ref: 'ChatUser' }) createdBy: string;
    @Prop() description?: string;
    @Prop({ default: false }) isArchived?: boolean;
    @Prop({ type: [Types.ObjectId], ref: 'ChatUser' }) users: string[];
    @Prop() jobProUserIds: string[];
    @Prop({ required: true }) tenantId: string;
    @Prop() createdAt: Date;
    @Prop() updatedAt: Date;
    @Prop({ default: false }) isPrivate: boolean;
    @Prop({ type: [Types.ObjectId], ref: 'ChatMessage' }) messages?: string[];
    @Prop() archivedBy?: string;
    @Prop() chatType: CHAT_TYPE;
    @Prop({ default: false }) isGeneral: boolean;
    @Prop() symmetricKey: string;
    @Prop() encryptedKeys: Array<EncryptedKey>;
    @Prop({ default: 0 }) lastMessageTimestamp: number;
    @Prop({ default: false }) isPinned: boolean;
    @Prop() pinnedBy: string;
    //   @Prop({ default: false }) isDeleteable:boolean;

    id?: string;

}

export const ChatSchema = SchemaFactory.createForClass(Chat)

export enum CHAT_TYPE {
    CIRCLE = 'CIRCLE',
    DM = 'DM',
}