/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Post, Body, Get, Param, Req } from '@nestjs/common';
import { MessagePattern, Payload, Ctx, RmqContext } from '@nestjs/microservices';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { ChatService } from 'src/chat/services/chat.service';
import { ChatUserDto, CreateChatUserDto, JobleServiceUserEvent } from 'src/dto/dtos';
import { GetUser } from 'src/get-user.decorator';
import { Public } from 'src/is_public';
import { LogService } from 'src/log/log.service';
import { CreateUserDto } from 'src/user/dto/create-user.dto';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';

@ApiTags('Chat User')
@Controller('v2/user')
export class ChatuserController {
    constructor(private chatUserService: ChatuserService, private logService: LogService
    ) { }

    @Post()
    createUser(@Body() data: CreateChatUserDto) {
        return this.chatUserService.createUser(data);
    }

    @Get('port')
    portUser() {
        return this.chatUserService.portUser();
    }
    @Post('switch-chat/:chatId')
    switchActiveChat(@Param('chatId') chatId: string, @GetUser() user: ChatUserDto) {
        this.logService.log('new active chat: ', chatId, user.id);
        return this.chatUserService.switchActiveChat(chatId, user);
    }
    @Public()
    @Get('get-by-id/:id')
    getUserById(@Param('id') id: string) {
        return this.chatUserService.findUser(id)
    }
    @Public()


    @Get('get-by-job-id/:id')
    getUserByJobId(@Param('id') id: string,) {

        return this.chatUserService.findUserByJobProId(id)
    }
}
