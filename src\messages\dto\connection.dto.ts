/* eslint-disable prettier/prettier */
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { UserDto } from 'src/user/dto/user.dto';

export class ConnectionDto {
    @ApiProperty()
    @IsString() userId: UserDto | string;

    @ApiProperty()
    @IsString() sender: UserDto | string;

    @ApiProperty()
    @IsOptional()
    @IsString() message?: string;

    @ApiProperty()
    @IsString()
    @IsOptional() status?: string;

    @ApiProperty()
    @IsBoolean()
    @IsOptional() isArchived?: boolean;

    @ApiProperty()
    @IsString()
    @IsOptional() id?: string;

}
