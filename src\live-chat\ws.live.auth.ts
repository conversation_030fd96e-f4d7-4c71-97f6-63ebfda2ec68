
import {
    CanActivate,
    ExecutionContext,
    Injectable,
    UnauthorizedException,

} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Request } from 'express';
import { Socket } from 'socket.io';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { LiveChatService } from './services/live-chat.service';
import { LiveChatUser } from './models';
import { ChatUser } from 'src/models/chat-user.model';
import { LIVE_EVENT_NAME } from './dtos';
import { LogService } from 'src/log/log.service';

@Injectable()
export class WSLiveAuthGuard implements CanActivate {
    constructor(private jwtService: JwtService, private configService: ConfigService, private chatUserService: ChatuserService, private logService: LogService, private liveChatService: LiveChatService) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToWs().getClient() as Socket;
        const token = this.extractTokenFromHeader(request);
        // this.logService.log('secret: ', this.configService.get('AUTH_SCREET'))

        // this.logService.log('token gotten: ', token)
        if (!token) {
            throw new UnauthorizedException();
        }
        try {
            const payload = await this.jwtService.decode(token);
            // this.logService.log('payload: ', payload)
            const email = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'];
            const tenantId = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid'];
            const jobProUserId = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'];
            const name = payload["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"];

            let foundUser = await this.liveChatService.getUserById(payload['id']);
            if (!foundUser && jobProUserId) foundUser = await this.liveChatService.getUserByJobId(jobProUserId)
            this.logService.log('foundUser in LiveEventAuth ..: ', foundUser);
            // if (!foundUser && !email && !tenantId && !jobProUserId) {
            //     const id = payload['id']
            //     foundUser = await this.liveChatService.getUserById(id);
            // }
            if (!foundUser) { foundUser = (await this.liveChatService.registerUser({ jobId: jobProUserId, tenantId: tenantId, email: email, })).user; }
            this.logService.log('foundUser in LiveEventAuth: ', foundUser);
            request['user'] = foundUser;
            request['tenantId'] = tenantId;
            request['jobProUserId'] = jobProUserId;
            request['liveUserId'] = foundUser['id'] ?? payload['id'] ?? foundUser['_id'];

        } catch (error) {
            this.logService.log('Ws error: ', error);
            request.emit(LIVE_EVENT_NAME, { event: 'AUTH_ERROR', error: error })
            throw new WsException('You are not Authorized to use this service');
        }
        return true;
    }

    private extractTokenFromHeader(request: Socket): string | undefined {
        const { authorization } = request.handshake.auth;
        this.logService.log('live chat authorization: ', authorization)
        return authorization ? authorization : undefined;
    }
}  