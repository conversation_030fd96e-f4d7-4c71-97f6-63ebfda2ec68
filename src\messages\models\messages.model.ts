import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { fieldEncryption } from 'mongoose-field-encryption';
import { FileDto } from '../dto/file.dto';
import { Reaction } from './reaction.model';
import { ReactionDto } from '../dto/reaction.dto';
// const mongooseFieldEncryption = require("mongoose-field-encryption").fieldEncryption;

@Schema({
  timestamps: true,
  toObject: { virtuals: true },
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret.__v;
      ret.id = ret._id;
      delete ret._id;
    },
  },
})
export class Message {
  @Prop({ type: Types.ObjectId, ref: 'User' }) user: string;
  @Prop() mentions: string[];
  @Prop() text: string;
  @Prop() subdomain: string;
  @Prop({ default: false }) is_private: boolean;
  @Prop() files: Array<FileDto>;
  @Prop({ type: Types.ObjectId, ref: 'Room' }) room?: string;
  @Prop() receiverId?: string;
  @Prop({ type: Types.ObjectId, ref: 'Message' }) parentId?: string;
  @Prop({ type: [Types.ObjectId], ref: 'User' }) readBy?: string[];
  @Prop() reactions: [{ user: string, reaction: string }]
  @Prop() messageType?: string;
  @Prop({ unique: true }) messageId?: string;
  @Prop() createdAt?: Date;
  @Prop() isDeleted?: boolean;
  @Prop() isPinned?: boolean;
  @Prop() pinnedBy?: string;
  @Prop() duration: string;
  @Prop() callType: string;

}
export const MessageSchema = SchemaFactory.createForClass(Message);
MessageSchema.plugin(fieldEncryption, {
  fields: ["text"],
  secret: process.env.MESSAGE_ENC_SECRET || '!325043439SKLEF232',
  encryptNull: false
})
