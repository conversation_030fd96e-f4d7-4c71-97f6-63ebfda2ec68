/* eslint-disable prettier/prettier */
/**
 * This model keeps record of connection request
 */

import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';

@Schema({
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class Connection {
    @Prop({ type: Types.ObjectId, ref: 'User', required: true }) userId: string;
    @Prop({ type: Types.ObjectId, ref: 'User', required: true }) sender: string;
    @Prop({ type: String, default: 'pending' }) status: string;
    @Prop() message: string;
    @Prop() isArchived: Boolean;
}
export const ConnectionSchema = SchemaFactory.createForClass(Connection);