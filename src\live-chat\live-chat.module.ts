import { LiveChatService } from './services/live-chat.service';
import { MongooseModule } from '@nestjs/mongoose';
import { LiveChatController } from './controllers/live-chat.controller';
/*
https://docs.nestjs.com/modules
*/

import { forwardRef, Module } from '@nestjs/common';
import { LiveChatGateway } from './services/live-chat.gateway';
import { LiveChat, LiveChatSchema, LiveChatUser, LiveChatUserSchema, LiveMessage, LiveMessageSchema } from './models';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from 'src/chat/jwt.strategy';
import { UserModule } from 'src/user/user.module';
import { ConfigModule } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { LogModule } from 'src/log/log.module';

@Module({
    imports: [
        LogModule,
        PassportModule,
        forwardRef(() => UserModule),
        MongooseModule.forFeature([
            { name: LiveMessage.name, schema: LiveMessageSchema },
            { name: LiveChat.name, schema: LiveChatSchema },
            { name: LiveChatUser.name, schema: LiveChatUserSchema, }])
    ],
    controllers: [
        LiveChatController,],
    providers: [JwtStrategy,
        LiveChatService, JwtService, LiveChatGateway],
    exports: [LiveChatService]
})
export class LiveChatModule { }
