import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatMessage } from 'src/models/chat-message.model';
import { Chat, CHAT_TYPE } from 'src/models/chat.model';
import { ChatMessageService } from './chat-message.service';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { ChatService } from './chat.service';
import { LogService } from 'src/log/log.service';

@Injectable()
export class WorkCircleService {

    constructor(
        @InjectModel(Chat.name) private chatModel: Model<Chat>,
        @InjectModel(ChatMessage.name) private chatMessageModel: Model<ChatMessage>,
        private chatUserService: ChatuserService,
        private chatService: ChatService,
        private logService: LogService
    ) { }
    async getDailyUserSentMessageCount(jobId: string, startDate?: Date, endDate?: Date) {
        const user = await this.chatUserService.findUserByJobProId(jobId);

        let dateFilter: any = {};
        if (startDate && endDate) {
            dateFilter = { $gte: startDate, $lte: endDate };
        } else {
            const last7Days = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            dateFilter = { $gte: last7Days };
        }

        const messageCount = await this.chatMessageModel.countDocuments({ sender: user.id, createdAt: dateFilter });
        return { messageCount };
    }
    async getTopCollaborators(tenantId: string, jobId: string) {
        try {
            this.logService.log('JobId: ', jobId, ' tenantId: ', tenantId);
            const user = await this.chatUserService.findUserByJobProId(jobId);
            if (!user) {
                throw new Error('User not found');
            }
            const chats = await this.chatModel.find({ tenantId, users: user.id.toString() }).exec();

            const collaboratorCounts: { [collaboratorId: string]: number } = {};

            for (const chat of chats) {
                if (chat.chatType === CHAT_TYPE.DM) {
                    const messages = await this.chatMessageModel.find({
                        chatId: chat.id,
                        createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
                    }).exec();

                    for (const message of messages) {
                        if (message.sender !== user.id.toString()) {
                            collaboratorCounts[message.sender.toString()] = (collaboratorCounts[message.sender.toString()] || 0) + 1;
                        }
                    }
                } else if (chat.chatType === CHAT_TYPE.CIRCLE) {
                    const collaborators = chat.users.filter(id => id !== user.id.toString());

                    for (const collaboratorId of collaborators) {
                        const messages = await this.chatMessageModel.find({
                            chatId: chat._id,
                            senderId: collaboratorId,
                            createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
                        }).exec();
                        collaboratorCounts[collaboratorId] = (collaboratorCounts[collaboratorId] || 0) + messages.length;
                    }
                }
            }

            delete collaboratorCounts[user.id.toString()];

            const topCollaborators = Object.keys(collaboratorCounts)
                .sort((a, b) => collaboratorCounts[b] - collaboratorCounts[a])
                .slice(0, 6);

            const collaboratorsDMs = await Promise.all(topCollaborators.map(collaboratorId =>
                this.chatService.findDMChatByUserIds(user.id.toString(), collaboratorId, tenantId)
            ));

            return await Promise.all(topCollaborators.map(async (collaboratorId, index) => {
                const collaborator = await this.chatUserService.findUser(collaboratorId);
                return {
                    jobId: collaborator.jobProUserId,
                    dm: collaboratorsDMs[index],
                    callId: collaboratorsDMs[index].id,
                    user
                };
            }));
        } catch (error) {
            this.logService.error('Error getting top collaborators: ', error);
            throw error;
        }
    }

}
