import { PartialType } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsDate, IsDateString, IsOptional, IsString } from "class-validator";
import { FileDto } from "src/messages/dto/file.dto";

export class ScheduledMessageDto {

    @IsBoolean() isRecurring: boolean;
    @IsArray() days: string[];
    @IsString() time: string;
    @IsString() chatId: string;
    @IsDateString() dueDate: Date;
    @IsOptional()
    @IsString() userId: string;
    @IsOptional()
    @IsString() jobProUserId: string;
    @IsOptional()
    @IsString() text?: string;
    @IsOptional()
    @IsArray() files?: FileDto[];
    @IsOptional()
    @IsDate() createdAt?: Date;
    @IsOptional()
    @IsDate() updatedAt?: Date;
}

export class UpdateMessageScheduleDto extends PartialType(ScheduledMessageDto) { };