import { Injectable } from '@nestjs/common';
import { LogService } from 'src/log/log.service';
import * as fs from 'fs';
import * as path from 'path';

export interface EmailTemplateVariables {
  userName?: string;
  circleName?: string;
  mentionerName?: string;
  messageText?: string;
  workspaceName?: string;
  chatName?: string;
  actionUrl?: string;
  [key: string]: any;
}

export enum EmailTemplateType {
  CHAT_MENTION = 'chat_mention',
  ADDED_TO_CIRCLE = 'added_to_circle',
  UNREAD_MESSAGE_DIGEST = 'unread_message_digest'
}

@Injectable()
export class EmailTemplateService {
  private templatesPath: string;

  constructor(private logService: LogService) {
    this.templatesPath = path.join(process.cwd(), 'src', 'mail', 'templates');
  }

  /**
   * Load and process an email template with variable substitution
   * @param templateType The type of template to load
   * @param variables Variables to substitute in the template
   * @returns Processed HTML string
   */
  async getTemplate(templateType: EmailTemplateType, variables: EmailTemplateVariables): Promise<string> {
    try {
      const templatePath = path.join(this.templatesPath, `${templateType}.html`);
      
      if (!fs.existsSync(templatePath)) {
        throw new Error(`Template file not found: ${templatePath}`);
      }

      let htmlContent = fs.readFileSync(templatePath, 'utf8');
      
      // Replace template variables
      htmlContent = this.replaceVariables(htmlContent, variables);
      
      return htmlContent;
    } catch (error) {
      this.logService.error('Error loading email template: ', error);
      throw error;
    }
  }

  /**
   * Replace variables in the HTML template
   * @param html HTML content with placeholders
   * @param variables Variables to substitute
   * @returns HTML with variables replaced
   */
  private replaceVariables(html: string, variables: EmailTemplateVariables): string {
    let processedHtml = html;

    // Extract the anme from tha email address. If the <NAME_EMAIL> for eg, extract chidozi and capitalize the first letter.
    var name = variables.userName?.split('@')[0];
    name = name.charAt(0).toUpperCase() + name.slice(1);

    let mentioner: string | undefined;
    if (variables.mentionerName) {
        const baseName = variables.mentionerName.split('@')[0];
        mentioner = baseName.charAt(0).toUpperCase() + baseName.slice(1);
    } else {
        mentioner = ''; // or null, depending on what you want
    }

    // Replace common placeholders
    if (variables.userName) {
      processedHtml = processedHtml.replace(/\[User Name\]/g, name);
    }

    if (variables.circleName) {
      processedHtml = processedHtml.replace(/\[Circle Name\]/g, variables.circleName);
      // Also replace [Topic Name] with circle name for added_to_circle template
      processedHtml = processedHtml.replace(/\[Topic Name\]/g, variables.circleName);
    }

    if (variables.mentionerName) {
      processedHtml = processedHtml.replace(/\[Name\]/g, mentioner);
    }

    // If the messageText is too long, truncate it and add ... at the end
    if (variables.messageText && variables.messageText.length > 100) {
      variables.messageText = variables.messageText.substring(0, 97) + '...';
    }
    
    if (variables.messageText) {
      // Replace the example message text in the template
      processedHtml = processedHtml.replace(
        "You were just mentioned in a chat message.", 
        `${variables.messageText}`
      );
    }

    if (variables.actionUrl) {
      // Replace the action button href
      processedHtml = processedHtml.replace(/href="#"/g, `href="${variables.actionUrl}"`);
    }

    // Replace any other custom variables using the format [VariableName]
    Object.keys(variables).forEach(key => {
      const placeholder = `[${key}]`;
      const value = variables[key];
      if (value !== undefined && value !== null) {
        processedHtml = processedHtml.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value.toString());
      }
    });

    return processedHtml;
  }

  /**
   * Get mention email template with specific variables
   * @param mentionerName Name of the person who mentioned the user
   * @param userName Name of the mentioned user
   * @param circleName Name of the circle/chat
   * @param messageText The message text containing the mention
   * @param actionUrl URL to view the message
   * @returns Processed HTML for mention email
   */
  async getMentionTemplate(
    mentionerName: string,
    userName: string,
    circleName: string,
    messageText: string,
    actionUrl: string
  ): Promise<string> {
    const variables: EmailTemplateVariables = {
      mentionerName,
      userName,
      circleName,
      messageText,
      actionUrl
    };

    return this.getTemplate(EmailTemplateType.CHAT_MENTION, variables);
  }

  /**
   * Get added to circle email template with specific variables
   * @param userName Name of the user being added
   * @param circleName Name of the circle
   * @param inviterName Name of the person who added the user
   * @param actionUrl URL to view the circle
   * @returns Processed HTML for added to circle email
   */
  async getAddedToCircleTemplate(
    userName: string,
    circleName: string,
    inviterName: string,
    actionUrl: string
  ): Promise<string> {
    const variables: EmailTemplateVariables = {
      userName,
      circleName,
      inviterName,
      actionUrl
    };

    return this.getTemplate(EmailTemplateType.ADDED_TO_CIRCLE, variables);
  }

  /**
   * Get unread message digest template with specific variables
   * @param userName Name of the user
   * @param unreadCount Number of unread messages
   * @param actionUrl URL to view messages
   * @returns Processed HTML for unread message digest email
   */
  async getUnreadDigestTemplate(
    userName: string,
    unreadCount: number,
    actionUrl: string
  ): Promise<string> {
    const variables: EmailTemplateVariables = {
      userName,
      unreadCount: unreadCount.toString(),
      actionUrl
    };

    return this.getTemplate(EmailTemplateType.UNREAD_MESSAGE_DIGEST, variables);
  }
}
