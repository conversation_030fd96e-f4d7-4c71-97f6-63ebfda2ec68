import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { isValidObjectId, Types } from "mongoose";
import { fieldEncryption } from "mongoose-field-encryption";
import { FileDto } from "src/messages/dto/file.dto";


@Schema({
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class ChatMessage {

    @Prop() text: string;
    @Prop() files: FileDto[];
    @Prop({
        type: Types.ObjectId, ref: 'Chat', required: true, validate: {
            validator: isValidObjectId,
            message: props => `${props.value} is not a valid ObjectId`
        }
    }) chatId: string;
    @Prop({
        type: Types.ObjectId, ref: 'ChatUser', required: true, validate: {
            validator: isValidObjectId,
            message: props => `${props.value} is not a valid ObjectId`
        }
    }) sender: string | Types.ObjectId;
    @Prop() senderJobProUserId: string;
    @Prop() mentions: string[];
    @Prop() reactions: [{ userId: string, reaction: string, jobProUserId: string }];
    @Prop({ default: false }) isPinned: boolean;
    @Prop() pinnedBy: string;
    @Prop() createdAt: Date;
    @Prop() updatedAt: Date;
    @Prop({ default: false }) isFirstChatMessage: boolean;
    @Prop({ default: false }) isDeleted: boolean;
    @Prop() messageType: string;
    @Prop() callDuration: number;
    @Prop() callType: string;
    @Prop() readBy: string[];
    @Prop() callStatus: string;
    @Prop() callId: string;
    @Prop() addedJobProUserIds?: string[];
    @Prop() tenantId: string;
    @Prop({ type: Types.ObjectId, ref: 'ChatMessage' }) parentId: string;
    @Prop({ default: false }) e2e: boolean;
    @Prop({ default: false }) isSystemGenerated: boolean;
    @Prop({ default: false }) isEdited?: boolean;
    @Prop({ default: false }) isForwarded?: boolean;
    @Prop() fID: string;
    id?: string;
}


export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);
ChatMessageSchema.plugin(fieldEncryption, {
    fields: ["text"],
    secret: process.env.MESSAGE_ENC_SECRET || '!325043439SKLEF232',
    encryptNull: false
})