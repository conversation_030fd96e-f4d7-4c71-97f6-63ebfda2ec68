import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';



@Schema()
export class EventParticipant {

    @Prop({ unique: true }) userId: string;
    @Prop({ required: true }) eventId: string;
    @Prop({ required: true }) name: string;
    @Prop() media: string;
    @Prop() eventStartDate: Date;
    @Prop() eventEndDate: Date;
    @Prop() socketId: string;
    @Prop() eventName: string;
    @Prop() is_online: boolean;
    @Prop({ type: [Types.ObjectId], ref: 'EventParticipant' }) matches: string[];
}
export const EventParticipantSchema = SchemaFactory.createForClass(EventParticipant)