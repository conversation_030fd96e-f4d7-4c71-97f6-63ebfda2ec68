import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { Document } from 'mongoose';
import { MessageType } from "./dtos";
export class LiveChatFile {

    fileName: string;

    fileSize: number;

    fileUrl: string;
}

@Schema({
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class LiveMessage {
    @Prop({ required: true, enum: ['text', 'file'] })
    messageType: string;
    @Prop() chatId: string;
    @Prop({ required: true })
    content: string;

    @Prop({ required: false })
    files: LiveChatFile[]
    @Prop({ default: false })
    isSystemGenerated: boolean;
    @Prop({ required: true })
    createdAt: Date;

    @Prop({ required: true })
    senderId: string;
}

@Schema({
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class LiveChat {
    @Prop({ type: Types.ObjectId, ref: "LiveChatUser", required: true }) clientId: string;
    @Prop({ type: Types.ObjectId, ref: "LiveChatUser", }) supportRepId: string;

    @Prop({ required: true, enum: ['open', 'closed'] })
    status: string;

    @Prop({ default: Date.now() })
    createdAt: Date;

    @Prop({ required: true, ref: 'LiveMessage', type: [Types.ObjectId] })
    messages: LiveMessage[];
}
@Schema({
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret.__v;
            ret.id = ret._id;
            delete ret._id;
        },
    },
    toObject: { virtuals: true },
})
export class LiveChatUser {
    @Prop()
    jobId: string;

    @Prop()
    email: string;

    @Prop()
    phone: string;
    @Prop()
    firstName: string;
    @Prop()
    lastName: string;
    @Prop()
    @Prop() profilePicture: string;
    tenantId: string;

    @Prop()
    role: string;
    @Prop({ default: Date.now() })
    createdAt?: Date;

    @Prop({ default: Date.now() })
    updatedAt?: Date;
    @Prop() id?: string;
}

export const LiveChatUserSchema = SchemaFactory.createForClass(LiveChatUser)
export const LiveChatSchema = SchemaFactory.createForClass(LiveChat);
export const LiveMessageSchema = SchemaFactory.createForClass(LiveMessage);
