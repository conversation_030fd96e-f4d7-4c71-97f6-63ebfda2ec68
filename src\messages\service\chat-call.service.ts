/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserService } from 'src/user/services/v1/user.service';
import { CHAT_CALL_STATUS, ChatCallDto } from '../dto/chat-call.dto';
import { Call } from '../models/chat_call.model';

@Injectable()
export class ChatCallService {
    async findOngoingCall(meetingId: string) {
        return this.callModel.findOne({ meetingId, status: CHAT_CALL_STATUS.ACCEPTED });
    }

    constructor(@InjectModel(Call.name) private readonly callModel: Model<Call>,
        private userService: UserService) { }
    async getByMeetingId(meetingId: string) {
        return await this.callModel.findOne({ meetingId });
    }

    async create(callData: ChatCallDto) {
        const found = await this.findByCallId(callData.callId);
        if (found) {
            await this.callModel.updateOne({ callId: callData.callId }, callData);
            return await this.findByCallId(callData.callId)
        } else
            return await this.callModel.create(callData);
    }

    async findByCallId(callId: string): Promise<ChatCallDto> {
        const found = await this.callModel.findOne({ callId })
        return found?.toJSON()
    }
    async findCallById(id: string) {
        return (await this.callModel.findById(id)).toJSON()
    }
    async findUserCallLogs(userId: string) {
        const user = await this.userService.findOne(userId);
        return await this.callModel.find({ or: [{ callerId: userId }, { receiverId: userId }, { room: { $in: user.rooms } }] })
    }
}
