/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { LiveChatService } from '../services/live-chat.service';
import { Public } from 'src/is_public';
import { ApiTags } from '@nestjs/swagger';
import { LiveChatUserDto, UpdateProfilePictureDto } from '../dtos';
import { GetUser } from 'src/get-user.decorator';
import { LiveChatUser } from '../models';
import { LogService } from 'src/log/log.service';

@ApiTags('Live Chat')
@Controller('v1/live-chat')
export class LiveChatController {
    constructor(private liveChatService: LiveChatService, private logService: LogService) { }

    @Public()
    @Post('register-user')
    registerUser(@Body() guestUser: LiveChatUserDto) {
        return this.liveChatService.registerUser(guestUser);
    }
    @Post('update-profile-picture')
    updateProfilePicture(@Body() data: UpdateProfilePictureDto, @GetUser() user: LiveChatUser) {
        this.logService.log("data: ", data, ' user: ', user)
        return this.liveChatService.updateUserProfilePicture(user['id'] ?? user['_id'], data.profilePicture, user.firstName, user.lastName);
    }
    @Get('get-open-chats')
    getOpenChats() {

        return this.liveChatService.getOpenChats();
    }
    @Get('get-chat-by-id/:id')
    getChatById(@Param('id') id: string) {
        return this.liveChatService.getChatWithMessages(id)
    }
}

