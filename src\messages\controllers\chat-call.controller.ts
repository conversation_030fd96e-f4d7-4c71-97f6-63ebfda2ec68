/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Get, Param } from '@nestjs/common';
import { ChatCallService } from '../service/chat-call.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('ChatCall V1')
@Controller('v1/chatcall')
export class ChatCallController {
    constructor(private chatCallService: ChatCallService) { }
    @Get('getbymeetingid/:meetingId')
    getCallByMeetingId(@Param('meetingId') meetingId: string) {
        return this.chatCallService.getByMeetingId(meetingId);
    }
}
