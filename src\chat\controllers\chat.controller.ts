/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Delete, Get, Param, Patch, Query } from '@nestjs/common';
import { ChatService } from '../services/chat.service';
import { MessagePattern, Payload, Ctx, RmqContext } from '@nestjs/microservices';
import { JobleServiceUserEvent, CreateChatUserDto, ChatDto, ChatUserDto } from 'src/dto/dtos';
import { ChatuserService } from 'src/user/services/v2/chatuser.service';
import { CHAT_TYPE } from 'src/models/chat.model';
import { GetUser } from 'src/get-user.decorator';
import { ChatUser } from 'src/models/chat-user.model';
import { ApiTags } from '@nestjs/swagger';
import { Public } from 'src/is_public';
import { GetTenantId } from 'src/get-tenantId.decorator';
import { RabbitMQEvents, RabbitMQQueues } from 'src/rabbitmq/dtos';
import { SubscribeMessage } from '@nestjs/websockets';
import { GetJobProUserId } from 'src/get-joprouserid.decorator';
import { LogService } from 'src/log/log.service';

@ApiTags('Chat V2')
@Controller('v2/chat')
export class ChatController {
    constructor(private chatService: ChatService, private chatUserService: ChatuserService, private logService: LogService) { }

    @MessagePattern('user-created-event')
    async userCreated(@Payload() data: number[], @Ctx() context: RmqContext) {
        this.logService.log(`Pattern: ${context.getPattern()}`);
        const message = context.getMessage();

        // const channelRef = context.getChannelRef();
        const content = JSON.parse(message['content'].toString())
        const userData = content['data'] as JobleServiceUserEvent;
        this.logService.log('message from RMQ: ', content);
        const createUserDto = new CreateChatUserDto();
        createUserDto.jobProUserId = userData.Id;
        createUserDto.tenantId = userData.TenantId;
        createUserDto.email = userData.Email;

        const chatUser = await this.chatUserService.createUser(createUserDto);
        const generalChat = await this.chatService.findGeneralChat(createUserDto.tenantId);
        this.chatService.addUserToChat(chatUser.jobProUserId, chatUser.email, generalChat.id, generalChat)

    }

    @MessagePattern('workspace-created-event')
    async onboardTenant(@Payload() data: number[], @Ctx() context: RmqContext) {
        const message = context.getMessage();
        // const channelRef = context.getChannelRef();
        const content = JSON.parse(message['content'].toString())
        const userData = content['data'] as JobleServiceUserEvent;
        const createUserDto = new CreateChatUserDto();
        createUserDto.jobProUserId = userData.Id;
        createUserDto.tenantId = userData.TenantId;
        createUserDto.email = userData.Email;

        const chatUser = await this.chatUserService.createUser(createUserDto);
        const chatObj = new ChatDto();
        chatObj.chatType = CHAT_TYPE.CIRCLE;
        chatObj.tenantId = createUserDto.tenantId;
        chatObj.jobProUserIds = [createUserDto.jobProUserId]
        chatObj.users = [chatUser.id];
        chatObj.name = 'General';
        chatObj.isPrivate = false;
        chatObj.isGeneral = true;
        chatObj.description = 'A general circle for everyone in the workspace!';
        chatObj.createdBy = chatUser.id;
        const generalChat = await this.chatService.createChat(chatObj);

        this.chatService.addUserToChat(chatUser.jobProUserId, chatUser.email, generalChat.id, generalChat);

        // Create Ai Trainer circle
        const AiChatObj = new ChatDto();
        AiChatObj.chatType = CHAT_TYPE.CIRCLE;
        AiChatObj.tenantId = createUserDto.tenantId;
        AiChatObj.jobProUserIds = [createUserDto.jobProUserId]
        AiChatObj.users = [chatUser.id];
        AiChatObj.name = 'AI Training';
        AiChatObj.isPrivate = true;
        AiChatObj.isGeneral = false;
        AiChatObj.description = 'A circle for training the Ai for an organization workspace!';
        AiChatObj.createdBy = chatUser.id;
        const AiChat = await this.chatService.createChat(AiChatObj);

        this.chatService.addUserToChat(chatUser.jobProUserId, chatUser.email, AiChat.id, AiChat);
    }

    @MessagePattern(RabbitMQEvents.NOTIFICATION_MESSAGE_EVENT)
    newNotification(@Payload() data: number[], @Ctx() context: RmqContext) {
        const message = context.getMessage();
        // const channelRef = context.getChannelRef();
        const content = JSON.parse(message['content'].toString());
        this.logService.log('new notification event: ', content);
        return { ack: true }
    }
    @Public()
    @Get('update-keys')
    updateKeys() {
        return this.chatService.createSymetricKeyForParticipant();
    }
    @Get('get-chat-files/:chatId')
    getChatFiles(@Param('chatId') chatId: string, @GetUser() user: ChatUser) {
        return this.chatService.getChatFiles(chatId, user.id)
    }
    @Get('get-ai-training-chat')

    GetAiTrainingChat(@GetTenantId() tenantId: string) {
        return this.chatService.getAiTrainingChat(tenantId);
    }

    @Patch('archive/:chatId')
    archiveChat(@Param('chatId') chatId: string, @GetUser() user: ChatUserDto) {
        return this.chatService.archiveChat(chatId, user.id);
    }

    @Patch('unarchive/:chatId')
    unArchiveChannel(@Param('chatId') chatId: string, @GetUser() user: ChatUserDto) {
        return this.chatService.unArchieveChat(chatId, user.id);
    }

    @Get('get-archived-channels/')
    getArchivedChannels(@GetUser() user: ChatUserDto) {
        return this.chatService.getArchivedChats(user.id);
    }

    @Get('get-chat/:id')
    getChatById(@Param('id') chatId: string) {
        return this.chatService.findChatByIdPopulated(chatId);
    }

    // @Public()
    @Get('get-all-user-chats')
    getAllUserChats(@GetTenantId() tenantId: string, @GetUser() user: ChatUserDto) {
        this.logService.log('user: ', user, ' tenantId: ', tenantId)
        return this.chatService.findChatsForUser(user.id, tenantId);
    }

    @Public()
    @Delete('delete/:id')
    deleteChat(@Param('id') chatId: string): Promise<any> {
        return this.chatService.deleteChat(chatId)
    }

    // @Public()
    // @Get('unique-tenant-ids')
    // async getUniqueTenantIds(): Promise<string[]> {
    //     return this.chatService.findUniqueTenantIds();
    // }
    // @Public()
    // @Get('create-circle-for-all-workspace/:name')
    // async generateAiCircleForAll(@Param('name') circle: string): Promise<string[]> {

    //     return this.chatService.createCircleForAllWorkspace(circle);
    // }

    @Get('get-ai-training-circle')
    getAiTrainingCircle(@GetUser() user: ChatUser, @GetTenantId() tenantId: string) {
        // will retrieve ai-training circle for a workspace and it's chat messages
    }
    @Public()
    @Get('clean-duplicate-user-in-chat')
    cleanDuplicateUserInChat() {
        return { error: 'route deactivated' }
        // return this.chatService.cleanDuplicateUserInChat();
    }
    @Public()
    @Get('works')
    works() {
        return { message: 'api works ' }
    }
} 
