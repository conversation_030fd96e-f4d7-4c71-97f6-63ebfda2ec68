import { Injectable } from '@nestjs/common/decorators';
import { ExecutionContext } from '@nestjs/common/interfaces/features/execution-context.interface';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { IS_PUBLIC_KEY } from 'src/is_public';
import { Request } from 'express';
import { UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ChatuserService } from './services/v2/chatuser.service';
import { ConfigService } from '@nestjs/config';
import { LiveChatService } from 'src/live-chat/services/live-chat.service';
import { LogService } from 'src/log/log.service';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
    constructor(private jwtService: JwtService, private configService: ConfigService, private chatUserService: ChatuserService,
        private logService: LogService, private liveChatService: LiveChatService, private reflector: Reflector) {
        super();
    }


    async canActivate(context: ExecutionContext): Promise<boolean> {
        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        this.logService.log('Request url: ', request.url);
        const token = this.extractTokenFromHeader(request);
        this.logService.log('token: ', token);
        try {
            const payload = await this.jwtService.decode(token);
            // this.logService.log('payload: ', payload)
            const email = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'];
            const tenantId = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid'] || payload['tenantId'];
            const jobProUserId = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'];
            this.logService.log('email: ', email, 'tenantId: ', tenantId, 'jobProUserId: ', jobProUserId)
            let foundUser = null;
            if (request.url.includes('live-chat')) {
                foundUser = await this.liveChatService.getUserByJobId(jobProUserId);
                if (!foundUser) foundUser = await this.liveChatService.registerUser({ email, tenantId, jobId: jobProUserId })
            } else {
                foundUser = await this.chatUserService.findUserByJobProId(jobProUserId);
            } this.logService.log('foundUser in WSAuthGuard: ', foundUser);
            if (!foundUser && email && tenantId && jobProUserId) {
                // foundUser = await this.chatUserService.createUser({ jobProUserId, email, tenantId })
                // throw new UnauthorizedException()
            }
            request['user'] = foundUser;
            request['tenantId'] = tenantId;
            request['jobProUserId'] = jobProUserId;

        } catch (error) {
            this.logService.log('Ws error: ', error)
            throw new UnauthorizedException('You are not Authorized to use this service');
        }
        return true;
    }
    private extractTokenFromHeader(request: Request): string | undefined {
        let authorization = request.headers.authorization;
        // this.logService.log('auth token: ', authorization)
        if (authorization && authorization.includes(' ')) {
            const array = authorization?.split(' ');
            authorization = array.length > 1 ? array[1] : array[0];
        }
        this.logService.log('authorization: ', authorization);
        return authorization ? authorization : undefined;
    }
}
