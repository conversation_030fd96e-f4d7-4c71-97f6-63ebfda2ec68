/*
https://docs.nestjs.com/modules
*/

import { Module } from '@nestjs/common';
import { SendMailController } from './controllers/send-mail.controller';
import { MailgunService } from './services/mailgun.service';
import { EmailTemplateService } from './services/email-template.service';
import { MailerModule, MailerService } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { LogModule } from 'src/log/log.module';

// import { MailgunModule } from '@mindik/mailgun-nestjs';


@Module({
    imports: [
        LogModule
    ],
    controllers: [SendMailController],
    providers: [MailgunService, EmailTemplateService],
    exports: [MailgunService, EmailTemplateService]
})
export class MailModule { }
