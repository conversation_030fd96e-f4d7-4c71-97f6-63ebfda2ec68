{"name": "chatservice", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "dev": "nest start --watch", "dev:debug": "nest start --debug --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.856.0", "@aws-sdk/s3-request-presigner": "^3.856.0", "@google-cloud/storage": "^7.3.0", "@nestjs-modules/mailer": "^1.6.1", "@nestjs/axios": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.2", "@nestjs/microservices": "^10.3.0", "@nestjs/mongoose": "^10.0.1", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.2.7", "@nestjs/schedule": "^3.0.4", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.1.13", "@nestjs/websockets": "^10.2.7", "@socket.io/mongo-adapter": "^0.3.2", "@socket.io/redis-adapter": "^8.3.0", "@socket.io/redis-streams-adapter": "^0.2.2", "@types/nodemailer-mailgun-transport": "^1.4.6", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "artillery-engine-socketio-v3": "^1.2.0", "axios": "^1.5.1", "body-parser": "^1.20.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^3.6.0", "dotenv": "^16.3.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.0", "handlebars": "^4.7.8", "i": "^0.3.7", "ioredis": "^5.4.1", "mailgun.js": "^9.4.0", "mongoose": "^7.6.3", "mongoose-encryption": "^2.1.2", "mongoose-field-encryption": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.6", "nodemailer-mailgun-transport": "^2.1.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "redis": "^4.7.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.2", "socket.io-stream": "^0.5.3", "uuid": "^9.0.1", "uuidv4": "^6.2.13"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.9", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}