// import { Injectable } from '@nestjs/common';
// import { InjectModel } from '@nestjs/mongoose';
// import { Model, Schema } from 'mongoose'; 
// import { Room } from 'src/messages/models/room.model';
// import { User } from 'src/user/models/user.model';

// @Injectable()
// export class JoinedRoomService {
//   constructor( 
//     @InjectModel('User')
//     private readonly userModel: Model<User>,
//     @InjectModel('Room')
//     private readonly roomModel: Model<RoomDto>,
//   ) { }

//   async create(joinedRoom: {
//     user: User;
//     room: Room;
//     socketId: string;
//   }): Promise<JoinedRoomI> {
//     const newJoinedRoom = new this.joinedRoomModel(joinedRoom);
//     newJoinedRoom.save();
//     const { _id: joinedRoomId, user, room } = newJoinedRoom;
//     await this.addJoinedRoomToRoomAndUser(user._id, joinedRoomId, room._id);
//     return newJoinedRoom;
//   }

//   async findByUser(userId: string) {
//     return await this.joinedRoomModel.find({ user: userId });
//   }

//   async findByRoom(roomId: string) {
//     return await this.joinedRoomModel.find({ room: roomId });
//   }

//   async deleteBySocketId(socketId: string) {
//     // const deletedJoinedRoom = await this.joinedRoomModel.findOneAndDelete({
//     //   socketId,
//     // });
//     // const { _id: deletedJoinedRoomId, user, room } = deletedJoinedRoom;
//     // await this.removeJoinedRoomFromRoomAndUser(
//     //   user._id,
//     //   deletedJoinedRoomId,
//     //   room.id,
//     // );
//   }

//   async deleteAll() {
//     await this.joinedRoomModel.deleteMany();
//   }

//   private async addJoinedRoomToRoomAndUser(
//     userId: string,
//     joinedRoomId: string,
//     roomId: string,
//   ) {
//     await this.userModel.findByIdAndUpdate(
//       userId,
//       { $push: { joinedRooms: joinedRoomId } },
//       { new: true, useFindAndModify: false },
//     );
//     await this.roomModel.findByIdAndUpdate(
//       roomId,
//       { $push: { joinedUsers: joinedRoomId } },
//       { new: true, useFindAndModify: false },
//     );
//     return;
//   }
//   private async removeJoinedRoomFromRoomAndUser(
//     userId: string,
//     joinedRoomId: string,
//     roomId: string,
//   ) {
//     await this.userModel.findByIdAndUpdate(
//       userId,
//       { $pull: { joinedRooms: joinedRoomId } },
//       { new: true, useFindAndModify: false },
//     );
//     await this.roomModel.findByIdAndUpdate(
//       roomId,
//       { $pull: { joinedUsers: joinedRoomId } },
//       { new: true, useFindAndModify: false },
//     );
//     return;
//   }
// }
