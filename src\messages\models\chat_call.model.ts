import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { CHAT_CALL_FORMAT, CHAT_CALL_STATUS, CHAT_CALL_TYPE } from "../dto/chat-call.dto";

@Schema({ timestamps: true, })
export class Call {
    @Prop({ type: Types.ObjectId, ref: 'User' }) callerId: string;
    @Prop() callerName: string;
    @Prop() type: CHAT_CALL_TYPE;
    @Prop() meetingId: string;
    @Prop() title: string;
    @Prop() name: string;
    @Prop() format: CHAT_CALL_FORMAT;
    @Prop() callerSocket: string;
    @Prop() callId: string;
    @Prop({ default: 2 }) participantCount: number;
    @Prop() createdAt: Date;
    @Prop() updatedAt: Date;
    @Prop({ type: Types.ObjectId, ref: 'User' }) receiverId?: string;
    @Prop({ type: Types.ObjectId, ref: 'Room' }) room?: string;
    @Prop() profilePictureUrl?: string;
    @Prop() status?: CHAT_CALL_STATUS
    @Prop() callDuration?: number;
    @Prop() startTime?: Date;
    @Prop() endTime?: Date;


}

export const CallSchema = SchemaFactory.createForClass(Call);