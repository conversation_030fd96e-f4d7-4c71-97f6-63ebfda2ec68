Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF97F0) msys-2.0.dll+0x1FE8E
0007FFFFA8F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x67F9
0007FFFFA8F0  000210046832 (000210286019, 0007FFFFA7A8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA8F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA8F0  000210068E24 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABD0  00021006A225 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE4B560000 ntdll.dll
7FFE49D90000 KERNEL32.DLL
7FFE48C40000 KERNELBASE.dll
7FFE49EB0000 USER32.dll
7FFE49340000 win32u.dll
7FFE4A1C0000 GDI32.dll
7FFE48B20000 gdi32full.dll
7FFE48A80000 msvcp_win.dll
7FFE491A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE49820000 advapi32.dll
7FFE4A110000 msvcrt.dll
7FFE49560000 sechost.dll
7FFE49430000 RPCRT4.dll
7FFE48160000 CRYPTBASE.DLL
7FFE492C0000 bcryptPrimitives.dll
7FFE4A1F0000 IMM32.DLL
