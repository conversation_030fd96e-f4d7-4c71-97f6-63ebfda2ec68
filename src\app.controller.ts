import { <PERSON>, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { AppService } from './app.service';
import { MailgunService } from './mail/services/mailgun.service';
import { Public } from './is_public';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService, private mailgunService: MailgunService) { }

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }
  @Get('healthz')
  getHealthCheck(@Res() res: Response) {

    return res.status(200).json({
      "status": "Healthy",
      "message": "All systems are OK!",
      'Author': '<PERSON>'
    });
  }
  @Public()
  @Get('auto')
  async checkAuto() {
    const workspace = 'Zarttech';
    const circle = 'General';
    const html = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Mention</title>
        <style>
            body {
                margin: 0;
            }
    
            #container {
                background-color: #008CFE;
                padding: 20px;
                display: table;
                width: 100%;
                box-sizing: border-box;
            }
    
            #inner-container {
                border-radius: 8px;
                border: 1px solid #ccc;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                background-color: #fff;
                transition: box-shadow 0.3s ease;
                width: 70%;
                margin: 0 auto; /* Center the container */
                box-sizing: border-box;
                padding: 30px;
            }
    
            img {
                max-width: 100%;
                height: auto;
                padding: 20px 0px;
            }
    
            a {
                display: block;
                background-color: #008CFE;
              color: white !important;
                padding: 10px;
                border: none;
                cursor: pointer;
                text-decoration: none;
                text-align: center;
            }
        </style>
    </head>
    <body>
    
    <div id="container">
        <div id="inner-container">
            <img src="https://chats.pactocoin.com/public/images/Jop%20pro%402x-8.png" width="200">
            <div style="font-size: 32px;">You have a new mention in</div>
            <div>
                <span style="font-size: 52px; color: gray; font-weight: bold;">${workspace} </span>
                <span style="font-size: 50px; color: grey;">(Jobchat)</span>
            </div>
            <div style="font-size: 32px; font-weight: bold; padding: 10px; margin-top: 20px;">
                From <span style="color: grey;">#${circle}</span>
            </div>
            <a href="https://${workspace}.joble.jobpro.app/suite/pkg/chat/dashboard">Go to Dashboard</a>
        </div>
    </div>
    
    </body>
    </html>
    `
    await this.mailgunService.sendMail({ to: ['<EMAIL>', '<EMAIL>', '<EMAIL>'], subject: 'Testing email with maingun', html: html })

    return { message: 'App is configured for auto deploy' }
  }
  @Get('migrate')
  @Public()
  migrate() {
    return this.appService.migrateData();
  }
}
