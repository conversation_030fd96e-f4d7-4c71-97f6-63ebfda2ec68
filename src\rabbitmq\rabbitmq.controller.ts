/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller } from '@nestjs/common';
import { Ctx, EventPattern, MessagePattern, Payload, RmqContext } from '@nestjs/microservices';
import { RabbitMQService } from './rabbitmq.service';
import { RabbitMQEvents, RabbitMQQueues } from './dtos';
import { LogService } from 'src/log/log.service';

@Controller()
export class RabbitmqController {
    constructor(private rabbitMQService: RabbitMQService, private logService: LogService) { }

    @MessagePattern(RabbitMQEvents.NOTIFICATION_MESSAGE_EVENT)
    newNotification(@Payload() data: number[], @Ctx() context: RmqContext) {
        const message = context.getMessage();
        // const channelRef = context.getChannelRef();
        const content = JSON.parse(message['content'].toString());
        this.logService.log('new notification event: ', content);
        return { ack: true }
    }

    @EventPattern(RabbitMQQueues.NOTIFICATION_MESSAGE_QUEUE)
    handleMessageQueueEvent(data: any) {
        this.logService.log('Notification event data: ', data)
    }
    @EventPattern(RabbitMQQueues.WORKSPACE_CREATED_QUEUE)
    handleWorkspaceQueueEvent(data: any) {
        this.logService.log('workspace created event data: ', data)
    }
    @EventPattern(RabbitMQQueues.USER_CREATED_QUEUE)
    handleUserCreatedQueueEvent(data: any) {
        this.logService.log('User created event data: ', data)
    }
}
