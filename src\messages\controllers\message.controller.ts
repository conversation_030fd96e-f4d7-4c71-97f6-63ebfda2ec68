/* eslint-disable prettier/prettier */
/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { CreateMessageDto } from '../dto/create-message.dto';
import { PinMessageDto } from '../dto/pin-message.dto';
import { MessagesService } from '../service/messages.service';
import { LogService } from 'src/log/log.service';

@Controller('v1/message')
export class MessageController {
    constructor(private msgService: MessagesService, private logService: LogService) { }

    @Get('channelmessages/:channelId')
    getChannelMessages(@Param('channelId') id: string) {
        return this.msgService.getChannelMessages(id);
    }
    @Get('channelmessagetotalpage/:channelId')
    getCannelMessageTotalPage(@Param('channelId') id: string, @Query('pageSize') pageSize: number) {
        // this.logService.log(' pagesize: ', id, pageSize)
        return this.msgService.getChannelMessageTotalPage(id, pageSize)
    }
    @Get('privatemessagetotalpage/:userId')
    getPrivateMessageTotalPage(@Param('userId') id: string, @Query('uId') cId: string, @Query('pageSize') pageSize: number) {
        this.logService.log(' pagesize: ', id, pageSize)
        return this.msgService.getPrivateMessageTotalPage(id, cId, pageSize);
    }

    @Get('channelmessagesbypages/:channelId')
    getChannelMessagesByPages(@Param('channelId') id: string,
        @Query('page') page: number, @Query('pageSize') pageSize: number) {
        // this.logService.log('page and pagesize: ', page, pageSize)
        return this.msgService.getChannelMessagesByPages(id, page, pageSize);
    }

    @Post('pin-message')
    pinMessage(@Body() pinMessage: PinMessageDto) {
        this.logService.log('pin message: ', pinMessage);
        return this.msgService.pinMessage(pinMessage);
    }
    @Get('dialogs/:id')
    dialogs(@Param('id') userId: string) {
        return this.msgService.getDialogs(userId);
    }
    @Post()
    createMessage(@Body() data: CreateMessageDto) {
        this.logService.log('new msg: ', data)
        return this.msgService.create(data);
    }
}
