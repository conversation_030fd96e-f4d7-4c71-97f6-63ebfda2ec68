import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { EventCategory } from 'src/activity-log/activity-log.dto';
import { LogService } from 'src/log/log.service';
import { FileDto } from 'src/messages/dto/file.dto';
import { RoomDto, UpdateRoomDto } from 'src/messages/dto/room.dto';
import { UserDto } from 'src/user/dto/user.dto';
import { UserService } from 'src/user/services/v1/user.service';
import { ActivityLogService } from '../../activity-log/activity-log.service';
import { FileService } from '../../file/file.service';
import { AddUserToRoomDto } from '../dto/add_users_to_room.dto';
import { MessageDto } from '../dto/message.dto';
import { ConnectionService } from './connection.service';
import { MessagesService } from './messages.service';
import { CreateRoomDto } from '../dto/create-room.dto';
import { ChatCallService } from './chat-call.service';
import { CreateUserDto } from 'src/user/dto/create-user.dto';

@Injectable()
export class RoomService {

  constructor(
    @InjectModel('Room') private readonly roomModel: Model<RoomDto>,
    @InjectModel('User') private readonly userModel: Model<UserDto>,
    @InjectModel('Message') private readonly messageModel: Model<MessageDto>,
    private userService: UserService,
    private activityLogService: ActivityLogService,
    private fileService: FileService,
    private logService: LogService,
    private chatCallService: ChatCallService
  ) { }

  async addUsersToRoom(data: AddUserToRoomDto) {
    try {
      const room = await this.getOnlyRoomData(data.roomId);
      for (let i = 0; i < data.users.length; i++) {
        let user = await this.userService.findByUserIdWithoutMsg(data.users[i].Id);
        if (!user) {
          const userObj: CreateUserDto = {
            username: data.users[i].FirstName + ' ' + data.users[i].LastName,
            email: data.users[i].Email,
            subdomain: room.subdomain,
            user_id: data.users[i].Id,
            phone: data.users[i].PhoneNo,
            companyId: data.users[i].CompanyId,
            tenantId: data.users[i].TenantId,
          };
          user = await this.userService.create(userObj);
        }
        this.logService.log('found user: ', user);
        await Promise.all([
          await this.addRoomToUser(user.id, data.roomId),
          await this.addUserToRoom(data.roomId, user.id),
        ]);
      }

      return await this.getRoomMembers(data.roomId);
    } catch (error) {
      this.logService.log('error adding users to room: ', error);
    }
  }
  async unArchieveChannel(channelId: string, userId: string) {
    try {
      if (!channelId)
        throw new BadRequestException(
          'Channel id is required to un-archive a channel',
        );
      const up = await this.roomModel.findOneAndUpdate(
        { _id: channelId },
        { isArchived: true },
      );
      // const activityLog = new ActivityLogDto()
      await this.activityLogService.logActivity({
        EventCategory: EventCategory.Chat,
        ActivitySummary: up.name + ' channel has been unarchived',
        Description: 'The channel has been un archived',
        EventId: channelId,
        UserId: userId,
        Application: 'Joble',
        By: userId,
      });
      if (up) {
        return { message: 'The channel has been successfully un-archived' };
      }
      return { message: 'un-archiving failed' };
    } catch (error) {
      this.logService.log('error: ', error);
      throw error;
    }
  }

  async getArchivedChannels(userId: string) {
    if (!userId)
      throw new BadRequestException(
        'user id is required to get archived channels',
      );
    const channels = await this.roomModel.find({
      users: userId,
      isArchived: true,
    });
    return channels;
  }
  async archieveChannel(channelId: string, userId: string) {
    try {
      if (!channelId)
        throw new BadRequestException(
          'Channel id is required to archive a channel',
        );
      if (!userId)
        throw new BadRequestException(
          'user id is required to archive a channel',
        );

      const up = await this.roomModel.findOneAndUpdate(
        { _id: channelId },
        { isArchived: true, archivedBy: userId },
      );

      await this.activityLogService.logActivity({
        EventCategory: EventCategory.Chat,
        ActivitySummary: up.name + ' circle has been archived',
        Description: 'The circle has been un archived',
        EventId: channelId,
        UserId: userId,
        Application: 'Joble',
        By: userId,
      });

      if (up) {
        return { message: 'cirlce has been successfully archived' };
      }
      return {
        message: 'circle archiving failed',
      };
    } catch (error) {
      this.logService.log('Archive error: ', error);
      throw error;
    }
  }

  async getRoomFiles(id: string) {
    this.logService.log('getting channel id: ', id);

    const files = await this.messageModel.aggregate([
      {
        $match: {
          room: id,
        },
      },
      {
        $group: {
          _id: null,
          mergedFiles: { $push: '$files' },
        },
      },
      {
        $project: {
          _id: 0,
          mergedFiles: {
            $reduce: {
              input: '$mergedFiles',
              initialValue: [],
              in: { $concatArrays: ['$$value', '$$this'] },
            },
          },
        },
      },
    ]);
    this.logService.log('messages: ', files);
    if (files.length) {
      return await Promise.all(
        files[0]?.mergedFiles?.map(async (file: FileDto) => {
          return {
            ...file,
            url: await this.fileService.getGCSFilePublicUrl(file.filename),
          };
        }),
      );
    } else return [];
  }
  async updateChannel(id: string, room: UpdateRoomDto): Promise<any> {
    if (!id) {
      throw new BadRequestException(
        'channel id is required to update a channel',
      );
    }
    const found = await this.roomModel.findOne({ _id: id });
    if (!found) {
      throw new NotFoundException(
        'Channel with the specified id does not exist',
      );
    }
    return await this.roomModel.updateOne({ _id: id }, room);
  }
  async getRoomMembers(roomId: string) {
    this.logService.log('roomId for members: ', roomId)
    if (!roomId || roomId == undefined) {
      throw new BadRequestException(
        'Channel Id is required to access members of a channel',
      );
    }
    const room = await this.roomModel
      .findOne({ _id: roomId })
      .populate({ path: 'users', model: 'User', select: '-rooms -connections' });
    // this.logService.log('members: ', room['users']);

    const users = room['users'];

    return users.map((user: any) => {
      return {
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePictureUrl,
        companyId: user.companyId,
        phone: user.phone,
        id: user.id,
      };
    });
  }
  async createRoom(room: any, creator: any, members?: any[], subdomain?: string): Promise<RoomDto> {
    try {
      this.logService.log(
        'Room: ',
        room,
        ' creator: ',
        creator,
        ' members: ',
        members?.length,
      );
      this.logService.log('creator: ', creator)
      let newRoom = new this.roomModel({ ...room, createdBy: new Types.ObjectId(creator['id']), subdomain, users: [new Types.ObjectId(creator['id'])] });

      newRoom = await newRoom.save();
      this.logService.log('intial saved room: ', newRoom);
      // await this.addCreatorToRoom(newRoom, creator);
      const roomUsers = newRoom.users;
      for (const user of roomUsers) {
        this.logService.log('room user: ', user);
        await this.addRoomToUser(user, newRoom._id);
      }
      this.logService.log('creator: ', creator);
      if (members?.length) {
        // create user account for members and add them to room
        for (let i = 0; i < members?.length; i++) {
          // await this.userModel.create(member);
          const userObj = {
            subdomain,
            email: members[i].Email,
            user_id: members[i].Id,
            username: members[i].UserName
              ? members[i].UserName
              : members[i].FirstName + ' ' + members[i].LastName,
            phone: members[i].PhoneNumber,
            password: members[i].FirstName,
            profilePictureUrl: members[i].ProfilePictureUrl
              ? members[i].ProfilePictureUrl
              : members[i].profilePictureUrl,
            tenantId: room.user.tenantId,
            companyId: room.user.companyId,
          };
          let user = await this.userService.userExist(userObj);
          if (!user) {
            user = await this.userService.create(userObj);
          }
          if (user) {
            // this.logService.log('saved user: ', user);
            await Promise.all([
              await this.addRoomToUser(user.id, newRoom.id),
              await this.addUserToRoom(newRoom.id, user.id),
            ]).then((result) => {
              this.logService.log(' added user to room: ', result);
            });
          }
        }
      }
      // const result = await this.getRoom(newRoom.id);

      this.activityLogService.logActivity({
        EventCategory: EventCategory.Chat,
        ActivitySummary: newRoom.name + ' channel has been created',
        Description: `a new channel with name ${newRoom.name} has been created by ${creator?.username}`,
        EventId: newRoom.id,
        UserId: creator.user_id,
        Application: 'Joble',
        By: creator.user_id,
      });
      const result = await this.getRoom(newRoom.id);
      this.logService.log('room created: ', result);
      return result;
    } catch (error) {
      this.logService.log('Error: ', error.message);
    }
  }
  async getOnlyRoomData(roomId: string) {

    if (!roomId) throw new BadRequestException('Room id is required');

    const found = await this.roomModel.findById(roomId).populate({
      path: 'users',
      model: 'User',
      select: '-connections -rooms'
    },);
    return found?.toJSON();
  }

  async getRoom(roomId: string): Promise<RoomDto> {
    this.logService.log('roomId: ', roomId);
    if (!roomId) throw new BadRequestException('Room id is required');
    const found = await this.roomModel.findById(roomId).populate({
      path: 'messages', // in room, populate messages
      populate: [
        {
          path: 'users',
        },
        {
          path: 'parentId', // in messages, populate parentId
          populate: { path: 'user' },
        },
      ],
    }).populate({
      path: 'users',
      model: 'User',
      select: '-connections -rooms'
    }).populate({
      path: 'createdBy',
      model: 'User',
      select: '-connections -rooms'
    });
    return found?.toJSON();
  }

  async getRoomsForUser(userId: string, subdomain: string) {
    const rooms = await this.roomModel
      .find(
        {
          $and: [
            { subdomain: subdomain },
            {
              $or: [
                { makeChannelPrivate: false },
                { users: new Types.ObjectId(userId) }
              ]
            }
          ]
        }
      )
      .populate({
        path: 'users',
        model: 'User',
        select: '-connections -rooms'
      }).populate({
        path: 'createdBy',
        model: 'User',
        select: '-connections -rooms'
      })

    // this.logService.log(' rooms for user: ', userId, rooms, 'subdomain: ', subdomain);

    const formatted = [];
    for (let i = 0; i < rooms.length; i++) {
      const unRead = await this.messageModel.countDocuments({ readBy: { $ne: userId }, room: new Types.ObjectId(rooms[i].id) });
      const call = await this.chatCallService.findOngoingCall(rooms[i].id.toString());
      this.logService.log('call for circle: ', call, rooms[i].name);
      this.logService.log('unread: ', unRead);

      formatted.push({ unRead: unRead, ...rooms[i]['_doc'], id: rooms[i].id, call });
    }
    // this.logService.log('formatted: ', formatted);
    return formatted;
    // if (formatted.length) {
    //   return formatted;
    // }
    // //  createGeneral channel
    // const room = new CreateRoomDto();
    // room.createdAt = new Date();
    // room.description = 'A circle for all members of a workspace to collaborate';
    // room.name = 'General';

    // const creator = {
    //   id: userId,
    //   subdomain: subdomain
    // }

    // const newRoom = await this.createRoom(room, creator, [], subdomain);
    // return [newRoom];
  }


  addCreatorToRoom(room: any, creator: any) {

    room.users.push(new Types.ObjectId(creator.id));
    room.createdBy = new Types.ObjectId(creator.id);
    return room;
  }

  async addUserToRoom(roomId: string, userId: string) {
    this.logService.log('adding user to circle: ', roomId, userId)
    const upd = await this.roomModel.findOneAndUpdate(
      { _id: roomId, users: { $ne: new Types.ObjectId(userId) } },
      { $push: { users: new Types.ObjectId(userId) } },
      { new: true } // To return the updated document
    );
    this.logService.log('upd: ', upd);
    return upd;
  }

  async addRoomToUser(userId: any, roomId: any) {
    this.logService.log('user to add: ', userId, 'roomId: ', roomId);
    const ups = await this.userModel.findByIdAndUpdate(
      userId,
      { $push: { rooms: new Types.ObjectId(roomId) } },
      { new: true, useFindAndModify: false },
    );
    // this.logService.log('add to room ups: ', ups);
    return ups;
  }
  async leaveRoom(
    roomId: string,
    userId: string,
  ): Promise<{ message: string }> {
    if (!roomId || !userId) {
      throw new BadRequestException(
        'channel id and user id are required to remove user from a channel',
      );
    }

    await this.removeRoomFromUser(userId, roomId);
    await this.removeUserFromRoom(roomId, userId);

    const room = await this.roomModel.findOne({ _id: roomId });
    const user = await this.userModel.findOne({ user_id: userId });

    await this.activityLogService.logActivity({
      EventCategory: EventCategory.Chat,
      ActivitySummary: `${user?.username} has left ${room.name} circle`,
      Description: `${user?.username} has left ${room.name} circle`,
      EventId: roomId,
      UserId: userId,
      Application: 'Joble',
      By: userId,
    });

    return { message: 'User has been removed from the channel' };
  }
  async removeUserFromRoom(roomId: string, userId: string) {
    this.logService.log(' roomId: ', roomId, ' userId: ', userId)
    const up = await this.roomModel.findByIdAndUpdate(
      roomId,
      { $pull: { users: new Types.ObjectId(userId) } },
      { new: true, useFindAndModify: false },
    );
    this.logService.log('updted room: ', up)
    return up;
  }

  async removeRoomFromUser(userId: string, roomId: string) {
    return await this.userModel.findByIdAndUpdate(
      userId,
      { $pull: { rooms: roomId } },
      { new: true, useFindAndModify: false },
    );
  }
} 
