import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateUserDto {
  @ApiProperty()
  @IsNotEmpty()
  username: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  user_id: string;

  @ApiProperty()
  @IsString()
  phone: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  profilePictureUrl?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  tenantId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  companyId?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  subdomain?: string;
}
