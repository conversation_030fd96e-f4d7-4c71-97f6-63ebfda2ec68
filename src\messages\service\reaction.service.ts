/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Reaction } from '../models/reaction.model';
import { Model, Types } from 'mongoose';
import { ReactionDto } from '../dto/reaction.dto';

@Injectable()
export class ReactionService {
    constructor(@InjectModel(Reaction.name) private readonly reactionModel: Model<ReactionDto>) { }

    async createReaction(reaction: ReactionDto) {
        const saved = await this.reactionModel.create({ reaction: reaction.reaction, user: new Types.ObjectId(reaction.user), message: reaction.messageId });
        await saved.populate([{
            path: 'user',
            model: 'User',
            select: 'username id'
        }]);
        return saved;
    }
    async removeReaction(reactionId: string) {
        const found = await this.reactionModel.findOneAndRemove({ _id: reactionId });
        return found;
    }
}
