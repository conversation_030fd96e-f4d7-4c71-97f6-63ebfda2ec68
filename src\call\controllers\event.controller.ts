/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { ScheduleEventDto } from '../dto/eventschedule.dto';
// import { UserAvailabilityDto } from '../dto/user-availability.dto';
import { EventService } from '../services/event.service';
import { InterestAreaDto } from '../dto/interest_area.dto';

@Controller('v1/event')
export class EventController {
    constructor(private eventService: EventService) { }
    @Post('schedule-meeting')
    scheduleMeeting(@Body() data: ScheduleEventDto) {
        return this.eventService.scheduleMeeting(data);
    }
    @Get('getmyinvites/:ticketId')
    getMyInvites(@Param('ticketId') ticketId: string) {
        return this.eventService.getMyInvites(ticketId);
    }
    @Post('create-availability')
    createAvailability(@Body() data: any) {
        return this.eventService.createAvailability(data);
    }
    @Get('get-announcements/:id')
    getAllAnnouncements(@Param('id') eventId: string) {
        return this.eventService.getAllAnnouncements(eventId)
    }
    @Get('get-availability')
    getAvailability(
        @Query('userId') userId: string,
        @Query('eventid') eventId: string,
    ) {
        console.log('availabity params: ', userId, eventId);
        return this.eventService.getAvailability(userId, eventId);
    }
    @Get('get-schedules')
    getSchedule(
        @Query('userId') userId: string,
        @Query('eventid') eventId: string,
    ) {
        console.log('schedule params: ', userId, eventId);
        return this.eventService.getSchedules();
    }
    @Post('register-interest')
    registerInterest(@Body() data: InterestAreaDto) {
        console.log('interest: ', data);
        return this.eventService.registerInterestArea(data);
    }
    @Get('get-my-interest')
    getAllInterestArea(
        @Query('userId') userId: string,
        @Query('eventId') eventId: string,) {
        console.log('eventId: ', eventId, ' userId: ', userId);
        return this.eventService.getMyInterestArea(userId, eventId);
    }
    @Get('get-all-participants/:eventId')
    getAllParticipants(
        @Query('userId') userId: string,
        @Param('eventId') eventId: string) {
        console.log('eventId: ', eventId);
        return this.eventService.getAllParticipants(eventId)
    }
    @Patch('update-schedule/:id')
    updateSchedule(@Body() schedule: any, @Param('id') id: string) {
        console.log(' update: ', id);
        return this.eventService.updateSchedule(id);
    }
    @Post('match-user')
    matchUser(@Body() data: { senderName: string, userId: string, eventId: string, interests: string[] }) {
        console.log('match data: ', data);
        return this.eventService.matchUser(data.senderName, data.userId, data.eventId, data.interests);
    }
    @Patch('update-match')
    updateMatch(@Body() data: any) {
        return this.eventService.updateMatch(data);
    }

    @Post('register-participant')
    registerParticipant(@Body() participant: any) {
        return this.eventService.registerParticipant(participant);
    }
}