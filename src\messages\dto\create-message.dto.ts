/* eslint-disable prettier/prettier */
import { ApiProperty, } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { UserDto } from 'src/user/dto/user.dto';
import { FileDto } from './file.dto';
import { MESSAGE_TYPE } from './message_type.enum';
import { RoomDto } from './room.dto';
import { CHAT_CALL_FORMAT } from './chat-call.dto';
export class CreateMessageDto {


    @ApiProperty()
    @IsBoolean()
    is_private: boolean;

    @ApiProperty()
    @IsOptional()
    @IsString()
    receiverId: string;

    @IsOptional()
    @IsArray()
    files?: FileDto[]

    @IsString()
    user: string | UserDto;

    @IsString()
    @IsOptional()
    room?: string | RoomDto;

    @IsOptional()
    @IsString()
    messageType?: MESSAGE_TYPE



    @ApiProperty()
    @IsArray()
    @IsOptional()
    mentions?: string[];

    @ApiProperty()
    @IsString()
    @IsOptional() subdomain?: string;



    @ApiProperty()
    @IsString()
    @IsOptional()
    parentId?: string;

    @IsOptional()
    @IsString()
    id?: string;

    @IsBoolean()
    @IsOptional()
    isDeleted?: boolean;

    @ApiProperty()
    @IsArray()
    @IsOptional()
    readBy?: string[];

    @ApiProperty()
    @IsString()
    @IsOptional()
    text?: string;

    @ApiProperty()
    @IsBoolean()
    @IsOptional()
    isPinned?: boolean;

    @IsString()
    @IsOptional()
    pinnedBy?: string;

    @IsString()
    @IsOptional()
    messageId?: string;

    @IsOptional()
    @IsString()
    duration?: string;

    @IsOptional()
    @IsString()
    callType?: CHAT_CALL_FORMAT
}