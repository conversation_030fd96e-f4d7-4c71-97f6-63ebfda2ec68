
import {
    CanActivate,
    ExecutionContext,
    Injectable,
    UnauthorizedException,

} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Request } from 'express';
import { ChatuserService } from './services/v2/chatuser.service';
import { Socket } from 'socket.io';
import { LogService } from 'src/log/log.service';

@Injectable()
export class WSAuthGuard implements CanActivate {

    constructor(private jwtService: JwtService, private configService: ConfigService, private chatUserService: ChatuserService, private logService: LogService) { }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToWs().getClient() as Socket;
        const token = this.extractTokenFromHeader(request);
        // this.logService.log('secret: ', this.configService.get('AUTH_SCREET'))
        this.logService.log('token gotten: ', token)
        console.log('token gotten: ', token)
        if (!token) {
            throw new WsException('Authentication token is required');
        }
        try {
            const secret = this.configService.get('AUTH_SCREET');
            if (!secret) {
                this.logService.log('JWT secret is not configured');
                throw new WsException('Server configuration error');
            }

            const payload = await this.jwtService.decode(token);
            // const payload = await this.jwtService.verifyAsync(token, { secret });
            this.logService.log('payload: ', payload)
            console.log('payload: ', payload)
            if (!payload) {
                this.logService.log('Invalid token payload');
                throw new WsException('Invalid token');
            }

            const email = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress'];
            const tenantId = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid'] || payload['tenantId'];
            const jobProUserId = payload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier'];
            this.logService.log('tenantId: ', tenantId)
            let foundUser = await this.chatUserService.findUserByJobProId(jobProUserId);
            // this.logService.log('foundUser in WSAuthGuard ..: ', foundUser);
            if (!foundUser && email && tenantId && jobProUserId) {
                foundUser = await this.chatUserService.createUser({ jobProUserId, email, tenantId })
            }
            this.logService.log('foundUser in WSAuthGuard: ', foundUser);
            request['user'] = foundUser;
            request['tenantId'] = tenantId;
            request['jobProUserId'] = jobProUserId;
            console.log('request: ', request['user'], ' tenantId: ', request['tenantId'], ' jobProUserId: ', request['jobProUserId']);

        } catch (error) {
            this.logService.log('Ws error: ', error)
            throw new WsException('You are not Authorized to use this service');
        }
        return true;
    }
    private extractTokenFromHeader(request: Socket): string | undefined {
        // Try to get token from handshake auth first
        const { authorization } = request.handshake.auth;
        if (authorization) {
            const token = authorization.toString().includes(' ') ? authorization.split(' ')[1] : authorization;
            if (token) return token;
        }
        
        // Try to get token from handshake headers as fallback
        const headerAuth = request.handshake.headers.authorization;
        if (headerAuth) {
            const token = headerAuth.toString().includes(' ') ? headerAuth.split(' ')[1] : headerAuth;
            if (token) return token;
        }
        
        // Try to get token from query parameters as another fallback
        const queryToken = request.handshake.query.token;
        if (queryToken) {
            return Array.isArray(queryToken) ? queryToken[0] : queryToken;
        }
        
        return undefined;
    }
}
