import { IsDate, IsNumber, IsOptional, IsString } from 'class-validator';

export class ChatCallDto {

    @IsString() callerId: string;
    @IsString() callerName: string;
    @IsString() type: CHAT_CALL_TYPE;
    @IsString() meetingId: string;
    @IsString() title: string;
    @IsString() name: string;
    @IsString() format: CHAT_CALL_FORMAT;
    @IsString() callerSocket?: string;
    @IsDate() createdAt?: Date;
    @IsDate() updatedAt?: Date;
    @IsString() receiverId?: string;
    @IsString() room?: string;
    @IsString() profilePictureUrl?: string;
    @IsString() status?: CHAT_CALL_STATUS;
    @IsNumber() callDuration?: number;
    @IsNumber() startTime?: number;
    @IsNumber() endTime?: number;
    @IsString()
    @IsOptional() callId?: string;
    @IsOptional()
    @IsString() messageId: string;
    @IsNumber()
    @IsOptional() participantCount?: number;
    @IsString()
    @IsOptional() chatId?: string;
}

export enum CHAT_CALL_FORMAT {
    VIDEO = 'video',
    AUDIO = 'audio',
}

export enum CHAT_CALL_TYPE {
    GROUP = 'group',
    PRIVATE = 'private',
}
export enum CHAT_CALL_STATUS {
    ACCEPTED = 'accepted',
    DECLINED = 'declined',
    RINGING = 'ringing',
    MISSED = 'missed',
    ENDED = "ended",
    CANCELLED = "CANCELLED",
    REJECTED = 'REJECTED',
    IN_PROGRESS = "In-Progress"
}

