import { HttpModule } from '@nestjs/axios';
import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MessageSchema } from 'src/messages/models/messages.model';
import { UserSchema } from '../models/user.model';
import { UserController } from './controllers/v1/user.controller';
import { UserService } from './services/v1/user.service';
import { RabbitmqModule } from 'src/rabbitmq/rabbitmq.module';
import { ChatuserController } from './controllers/v2/chatuser.controller';
import { ChatuserService } from './services/v2/chatuser.service';
import { ChatUser, ChatUserSchema } from 'src/models/chat-user.model';
import { ChatModule } from 'src/chat/chat.module';
import { Chat, ChatSchema } from 'src/models/chat.model';
import { LiveChatModule } from 'src/live-chat/live-chat.module';
import { LogModule } from 'src/log/log.module';

@Module({
  imports: [
    LogModule,
    RabbitmqModule,
    MongooseModule.forFeature([
      { name: 'User', schema: UserSchema },
      { name: 'Message', schema: MessageSchema },
      { name: ChatUser.name, schema: ChatUserSchema },
      { name: Chat.name, schema: ChatSchema }
    ]),
    HttpModule,
    forwardRef(() => ChatModule),
    forwardRef(() => LiveChatModule)
  ],
  controllers: [ChatuserController],
  providers: [ChatuserService],
  exports: [ChatuserService],
})
export class UserModule { }
