export const EVENT_NAME = 'message';

export class EventPayloadDto {
    event: MESSAGE_EVENT;
    data: any;
}
export enum MESSAGE_EVENT {
    CREATE_CIRCLE = 'create_cirlce',
    USER_DATA = 'user_data',
    CHAT_CALL = 'chat_call',
    CHAT_CALL_RESPONSE = 'chat_call_response',
    NEW_USER = 'new_user',
    REQUEST_CHANNELS = 'request_channels',
    JOIN_CIRCLE = 'join_circle',
    ADD_USERS_TO_CIRCLE = 'add_users_to_circle',
    LEAVE_CIRCLE = 'leave_circle',
    NEW_MESSAGE = 'new_message',
    GET_PRIVATE_MESSAGES = 'get_private_messages',
    GET_PRIVATE_MESSAGES_BY_PAGE = 'get_private_messages_by_page',
    TYPING = 'typing',
    DELETE_MESSAGE_ME = 'delete-message-me',
    DELETE_MESSAGE_ALL = 'delete_message_all',
    GET_ALL_UNREAD_MESSAGES = 'get_all_unread_messages',
    UPDATE_READ_STATUS = 'update_read_status',
    GET_USER_MESSAGES = 'get_user_messages',
    ADD_USER_TO_CIRCLE = 'add_user_to_circle',
    ADD_REACTION = 'add_reaction',
    REMOVE_REACTION = "remove_reaction",
    ADD_CONNECTION = 'add_connection',
    REQUEST_CONNECTIONS = 'request_connections',

}

export const CHAT_EVENT = 'chat-event';


export enum CHAT_EVENTS {
    GET_CHATS_WITH_UNREAD_COUNT = "GET_CHATS_WITH_UNREAD_COUNT",
    GET_CHAT_MESSAGES = "GET_CHAT_MESSAGES",
    GET_CHAT_PAGINATED_MESSAGES = "GET_CHAT_PAGINATED_MESSAGES",
    NEW_MESSAGE = 'NEW_MESSAGE',
    UPDATE_MESSAGE = 'UPDATE_MESSAGE',
    DELETE_MESSAGE = 'DELETE_MESSAGE',
    PIN_UNPIN_MESSAGE = 'PIN_UNPIN_MESSAGE',
    PIN_CHAT = 'PIN_CHAT',
    UPDATE_READ_STATUS = 'UPDATE_READ_STATUS',
    MARK_ALL_AS_READ = 'MARK_ALL_AS_READ',
    GET_ALL_UNREAD_MESSAGES = 'GET_ALL_UNREAD_MESSAGES',
    ADD_REACTION_TO_MESSAGE = 'ADD_REACTION_TO_MESSAGE',
    REMOVE_REACTION_FROM_MESSAGE = 'REMOVE_REACTION_FROM_MESSAGE',

    CREATE_CHAT = 'CREATE_CHAT',
    CHAT_CREATED = 'CHAT_CREATED',
    UPDATE_CHAT = 'UPDATE_CHAT',
    ADD_USERS_TO_CHAT = 'ADD_USERS_TO_CHAT',
    REMOVE_USER_FROM_CHAT = 'REMOVE_USER_FROM_CHAT',
    IS_TYPING = 'IS_TYPING',
    IS_RECORDING = 'IS_RECORDING',

    INITIATE_CALL = 'INITIATE_CALL',
    ANSwER_CALL = 'ANSWER_CALL',
    DECLINE_CALL = 'DECLINE_CALL',
    END_CALL = 'END_CALL',
    MISSED_CALL = "MISSED_CALL",
    CALL_RINGING = "CALL_RINGING",

    UPDATE_COLOR_PREFERENCE = "UPDATE_COLOR_PREFERENCE",
    GET_TOTAL_UNREAD_COUNT = "GET_TOTAL_UNREAD_COUNT",
    GET_USER_DATA = "GET_USER_DATA",
    JOIN_CIRCLE = "JOIN_CIRCLE",
    LEAVE_CHAT = "LEAVE_CHAT",
    NEW_ACTIVITY = "NEW_ACTIVITY",
    JOINED_CALL = "JOINED_CALL",
    ONLINE_STATUS = "ONLINE_STATUS",
    ONLINE_USERS = "ONLINE_USERS",
    CALL_CANCELED = "CALL_CANCELED",
    CHANGE_GROUP_TO_CIRCLE = "CHANGE_GROUP_TO_CIRCLE",
    ALL_CHATS = "ALL_CHATS",
    TOGGLE_STATUS = 'TOGGLE_STATUS',
    CHAT_CALL = "CHAT_CALL",
    BUSY = "BUSY",
    IGNORE_CALL = "IGNORE_CALL",
    ADD_A_UER_TO_ONGOING_CALL = "ADD_A_TO_ONGOING_CALL"
}
export enum USER_STATUSES {
    ACTIVE = 'Active',
    AWAY = 'Away',
    OUT_OF_OFFICE = 'OutOfOffice',
    OFFLINE = 'Offline'
}