/*
https://docs.nestjs.com/websockets/gateways#gateways
*/

import { MessageBody, SubscribeMessage, WebSocketGateway, WebSocketServer, OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit, ConnectedSocket } from '@nestjs/websockets';
import { ChatStatus, LIVE_CHAT_EVENTS, LIVE_EVENT_NAME, LiveChaatPayloadDto as LiveChatPayloadDto, LiveChatMessage, LiveChatUserDto } from '../dtos';
import { Server, Socket } from 'socket.io';
import { LiveChatService } from './live-chat.service';
import { WSLiveAuthGuard } from '../ws.live.auth';
import { UseGuards } from '@nestjs/common';
import { Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { createAdapter } from '@socket.io/redis-streams-adapter';
import { pubClient } from 'src/chat/redis.adapter';
import { LogService } from 'src/log/log.service';

@WebSocketGateway({
    cors: {
        origin: '*',
    },
    // namespace: 'live-chat',
    // Server: {
    //     adapter: createAdapter(pubClient)
    // }
})
export class LiveChatGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {

    @WebSocketServer()
    server: Server; EVENTS
    users = new Map<string, { socket: Socket, user: LiveChatUserDto }>();
    constructor(private liveChatService: LiveChatService,
        private logService: LogService, private configService: ConfigService,) { }

    async afterInit(server: Server) {
        // this.logService.log('Socket.IO initialized:', server);
        // this.logService.log('Adapter in chat.gateway:', server.sockets.adapter);
        // Set up the MongoDB adapter for Socket.IO
        // const mongoClient = new MongoClient(this.configService.get('MONGODB_URI'));
        // await mongoClient.connect();
        // const db = mongoClient.db('stagingDB');  // Replace with your database name
        // const collection = db.collection('sockets');
        // server.adapter(createAdapter(collection));
    }

    @UseGuards(WSLiveAuthGuard)
    @SubscribeMessage(LIVE_EVENT_NAME)
    handleEvent(@MessageBody() payload: LiveChatPayloadDto, @ConnectedSocket() client: Socket) {
        this.logService.log('Payload ', payload)
        const userId = client['liveUserId'];
        const jobId = client['jobProUserId'];
        const tenantId = client['tenantId'];
        this.logService.log('userId ', userId, 'jobId ', jobId);
        this.checkUser(userId, jobId, tenantId, client);

        switch (payload.event) {
            case LIVE_CHAT_EVENTS.START_CHAT:
                return this.startChat(payload, client, userId);
            case LIVE_CHAT_EVENTS.JOIN_CHAT:
                return this.joinChat(payload, client, jobId, userId);
            case LIVE_CHAT_EVENTS.NEW_MESSAGE:
                return this.handleNewMessage(payload, client, userId);
            case LIVE_CHAT_EVENTS.END_CHAT:
                return this.endChat(payload, client, userId);
            case LIVE_CHAT_EVENTS.UPGRADE_TO_TICKET:
                return this.upgradeChatToTicket(payload, client);
            case LIVE_CHAT_EVENTS.ADD_CHAT_REVIEW:
                return this.addChatReview(payload, client)
            case LIVE_CHAT_EVENTS.GET_CHAT_EXISTING_MESSAGES:
                return this.getChatExistingMessages(payload, client)
            case LIVE_CHAT_EVENTS.GET_OPEN_CHATS:
                return this.getOpenChats(payload, client);
            case LIVE_CHAT_EVENTS.GET_ACTIVE_CHATS:
                return this.getActiveChats(payload, client);
            case LIVE_CHAT_EVENTS.REJOIN_CHAT:
                return this.rejoinChat(payload, client, jobId, userId);
            case LIVE_CHAT_EVENTS.LEAVE_CHAT:
                return this.handleLeaveChat(payload, client, userId)
            default:
                client.emit(LIVE_EVENT_NAME, { ...payload, error: 'Unknown Event' })
        }
    }
    async handleLeaveChat(payload: LiveChatPayloadDto, client: Socket, supportRepId: string) {
        const { chatId } = payload.data;
        this.liveChatService.leaveChat(chatId, supportRepId)
    }
    async rejoinChat(payload: LiveChatPayloadDto, client: Socket, jobId: string, userId: string) {
        const { chatId } = payload.data;
        const chat = await this.liveChatService.getChatById(chatId);
        if (chat) {
            client.join(chatId)
            client.to(chatId).emit(LIVE_EVENT_NAME, { ...payload, data: chat });
        }
    }
    async getOpenChats(payload: LiveChatPayloadDto, client: Socket) {
        client.join('supportreps');
        this.logService.log('rooms: ', client.rooms);
        const chats = await this.liveChatService.getOpenChats();
        payload.data = chats;
        for (const chat of chats) {
            client.join(chat.id.toString())
        }
        client.emit(LIVE_EVENT_NAME, payload)
    }
    async getActiveChats(payload: LiveChatPayloadDto, client: Socket) {
        client.join('supportreps');
        this.logService.log('rooms: ', client.rooms);
        const chats = await this.liveChatService.getActiveChats();
        payload.data = chats;
        for (const chat of chats) {
            client.join(chat.id.toString())
        }
        client.emit(LIVE_EVENT_NAME, payload)
    }
    async getChatExistingMessages(payload: LiveChatPayloadDto, client: Socket) {
        const { chatId } = payload.data;
        client.join(chatId);
        const messages = await this.liveChatService.getChatMessages(chatId);
        payload.data = messages;
        client.emit(LIVE_EVENT_NAME, payload);
    }
    async checkUser(userId: string, jobId: string, tenantId: string, client: Socket) {
        if (!this.users.get(client.id)) {
            this.users.set(client.id, {
                socket: client,
                user: await this.liveChatService.getUserById(userId)
            })
        }
    }
    async startChat(payload: LiveChatPayloadDto, client: Socket, clientId: string) {
        const { firstName, lastName, profilePicture } = payload.data;
        this.logService.log('Client id: ', clientId);
        this.liveChatService.updateUserProfilePicture(clientId, profilePicture, firstName, lastName).then().catch(err => this.logService.log('update user error: ', err));
        const chat = await this.liveChatService.startChat({
            clientId: clientId,
            chatStatus: ChatStatus.open
        });
        this.logService.log('started chat: ', chat);
        client.join(chat.id.toString());
        payload.data = chat;
        const newPayload: LiveChatPayloadDto = { event: LIVE_CHAT_EVENTS.NEW_CHAT, data: chat };

        // this.logService.log('new chat payload: ', newPayload);
        client.broadcast.to('supportreps').emit(LIVE_EVENT_NAME, payload);
        client.emit(LIVE_EVENT_NAME, payload);

        //return payload;
    }

    async joinChat(payload: LiveChatPayloadDto, client: Socket, jobId: string, userId: string) {
        try {
            const { chatId, firstName, lastName, profilePicture } = payload.data;
            this.logService.log('chatId and jobId', chatId, jobId);

            this.liveChatService.updateUserProfilePicture(userId, profilePicture, firstName, lastName).then().catch(err => this.logService.log('update user error: ', err));

            const chat = await this.liveChatService.joinChat(jobId, chatId);
            this.logService.log('found chat: ', chat)
            client.join('supportreps');
            const joinMsg = {
                messageId: '1',
                content: 'You are now chatting with ' + firstName + ' ' + lastName,
                createdAt: new Date(),
                updatedAt: new Date(),
                chatId: chatId,
                isSystemGenerated: true
            }
            client.join(chatId.toString());
            client.to(chatId).emit(LIVE_EVENT_NAME, { event: LIVE_CHAT_EVENTS.NEW_MESSAGE, data: joinMsg })

            payload.data = chat;
            client.to('supportreps').emit(LIVE_EVENT_NAME, payload);
            client.to(chatId.toString()).emit(LIVE_EVENT_NAME, payload);
            client.emit(LIVE_EVENT_NAME, payload)
            this.logService.log('client rooms: ', client.rooms)
            //return payload;
        } catch (error) {
            this.logService.log('Error joining chat: ', error);
            payload.error = error;
            //return payload;
        }
    }

    async handleNewMessage(payload: LiveChatPayloadDto, client: Socket, userId: string) {
        // this.logService.log('adapter on send msg: ', this.server.sockets.adapter);
        try {
            const { chatId, content, files, messageType } = payload.data;
            // this.logService.log('rooms : ', client.rooms, chatId)
            const msg = new LiveChatMessage();
            msg.id = new Types.ObjectId().toString();
            msg.content = content;
            msg.files = files;
            msg.messageType = messageType;
            msg.senderId = userId;
            msg.createdAt = new Date();
            msg.updatedAt = new Date();
            msg.chatId = chatId;
            msg.isSystemGenerated = false
            client.join(chatId.toString())

            const saved = await this.liveChatService.addNewMessage(chatId, msg);

            // this.logService.log('rooms : ', client.rooms, chatId);

            payload.data = saved;
            // this.logService.log('saved message payload: ', payload)
            // client.to(chatId).emit(LIVE_EVENT_NAME, payload);
            this.server.to(chatId.toString()).emit(LIVE_EVENT_NAME, payload);
            this.logService.log('message sent...')
            // client.emit(LIVE_EVENT_NAME, payload);
            // this.logService.log('Adapter status:', this.server.sockets.adapter);

            //return payload;
        } catch (error) {
            this.logService.log('Error sending new live chat: ', error);
        }
    }
    async endChat(payload: LiveChatPayloadDto, client: Socket, userId) {
        try {
            const { chatId } = payload.data;
            const res = await this.liveChatService.closeChat(chatId, userId)

            payload.data = res;
            client.to(chatId).emit(LIVE_EVENT_NAME, payload);
            client.emit(LIVE_EVENT_NAME, payload);
            //return res;
        } catch (error) {
            this.logService.log('Error closing chat: ', error.message)
        }
    };
    upgradeChatToTicket(payload: LiveChatPayloadDto, client: Socket) {
        throw new Error('Method not implemented.');
    }
    addChatReview(payload: LiveChatPayloadDto, client: Socket) {
        throw new Error('Method not implemented.');
    }

    handleConnection(client: any, ...args: any[]) {
        this.logService.log('User connected to live chat', client.id);
    }

    handleDisconnect(client: Socket) {
        this.users.delete(client.id)
    }


}
