/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ChatMessageService } from '../services/chat-message.service';
import { GetUser } from 'src/get-user.decorator';
import { ChatUser } from 'src/models/chat-user.model';
import { ApiTags } from '@nestjs/swagger';
import { ChatMessageDto, ChatMessageHttpDto, ChatUserDto, MessagePaginationDto } from 'src/dto/dtos';
import { GetTenantId } from 'src/get-tenantId.decorator';
import { MessagePattern } from '@nestjs/microservices';
import { HttpmessageService } from '../services/httpmessage.service';
import { IsNumber } from 'class-validator';
import { Public } from 'src/is_public';
import { UserDto } from 'src/user/dto/user.dto';
import { LogService } from 'src/log/log.service';

@ApiTags('Chat  Message V2')
@Controller('v2/chatmessage')
export class ChatMessageController {
    constructor(private chatMessageService: ChatMessageService, private httpMessageService: HttpmessageService, private logService: LogService) { }

    @Get('dialogs/:id')
    getDialogs(@Param('id') userId: string, @Query('tenantId') tenantId: string) {
        this.logService.log('req: ', userId, tenantId)
        return this.chatMessageService.userDialogs(userId, tenantId)
    }
    @Get('updatemessage-tenantId')
    updateMessageTenantId() {
        return this.chatMessageService.updateMessageTenantId();
    }
    @Get('mentions')
    getActivities(@GetUser() user: ChatUser, @GetTenantId() tenantId: string) {
        // this.logService.log('req: ', user, tenantId)
        return this.chatMessageService.getMentions(user, tenantId)
    }
    @Get('reactions')
    getReactions(@GetUser() user: ChatUser, @GetTenantId() tenantId: string) {
        return this.chatMessageService.getReactions(user, tenantId);
    }
    @Get('invites')
    getInvites(@GetUser() user: ChatUser, @GetTenantId() tenantId: string) {
        return this.chatMessageService.getInvites(user, tenantId);
    }
    @Get('dialogs-count/:id')
    getDialogCount(@Param('id') userId: string, @Query('tenantId') tenantId: string) {
        return this.chatMessageService.getDialogCount(userId, tenantId)
    }
    /**
    * 
    * @param chatId 
    * @returns all files shared in a chat
    */
    @Get('get-chat-files/:chatId')
    getRoomFiles(@Param('chatId') chatId: string) {
        return this.chatMessageService.getChatFiles(chatId);
    }
    /**
   * 
   * @param chatId 
   * @returns all files shared in a chat by a user
   */
    @Get('get-chat-files-for-user/:chatId/:userId')
    getRoomFilesForUser(@Param('chatId') chatId: string, @Param('userId') userId: string) {
        return this.chatMessageService.getChatFilesForUser(chatId, userId);
    }
    @Post('create')

    createMessage(@Body() message: ChatMessageHttpDto, @GetUser() user: ChatUserDto, @GetTenantId() tenantId: string) {
        if (message.parentId == '' || message.parentId == null) {
            delete message.parentId;
        }
        if (!message?.senderJobProUserId) {
            message.senderJobProUserId = user.jobProUserId;
        }
        if (!message.addedJobProUserIds?.length) { message.addedJobProUserIds = [user.jobProUserId] }
        if (!message?.sender) {
            message.sender = user.id;
        }
        if (message.jobId && !message.chatId) {
            return this.httpMessageService.handleHttpChatMessage(message, user, tenantId)
        }
        return this.chatMessageService.createMessage(message as ChatMessageDto);
    }
    @Public()
    @Get('get-paginated-message/:chatId')
    getPaginatedMessages(@Query() params: MessagePaginationDto, @Param('chatId') chatId: string) {
        return this.chatMessageService.getPaginatedMessages(chatId, params.page, params.pageSize);
    }
    @Get('get-all-unread-messages/:chatId')
    getAllUnReadMessages(@Param('chatId') chatId: string, @GetUser() user: UserDto) {
        return this.chatMessageService.getChatAllUnReadMessages(chatId, user.id)
    }

    @Get('get-message/:messageId')
    getParentMessage(@Param('messageId') parentId: string) {
        return this.chatMessageService.getParentMessage(parentId)
    }
}
