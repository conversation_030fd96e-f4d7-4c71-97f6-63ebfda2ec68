import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret.__v;
      ret.id = ret._id;
      delete ret._id;
    },
  },
  toObject: { virtuals: true },
})
export class ChatActivity {
  @Prop({ type: Types.ObjectId, ref: 'ChatMessage' }) message: string
  @Prop({ type: Types.ObjectId, ref: 'ChatUser' }) sender: string;
  @Prop({ type: Types.ObjectId, ref: 'ChatUser' }) user: string;
  @Prop({ type: Types.ObjectId, ref: 'Chat' }) chat: string;
  @Prop() activityType: string;
  @Prop() inviteType: string;
  @Prop({ default: false }) isRead: boolean;
  @Prop() tenantId: string;
}
export const ChatActivitySchema = SchemaFactory.createForClass(ChatActivity)
