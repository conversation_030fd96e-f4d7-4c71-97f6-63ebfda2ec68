/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Get, Param, Query } from '@nestjs/common';
import { ChatUserDto } from 'src/dto/dtos';
import { GetTenantId } from 'src/get-tenantId.decorator';
import { GetUser } from 'src/get-user.decorator';
import { WorkCircleService } from '../services/work-circle.service';
import { UserDto } from 'src/user/dto/user.dto';
import { GetJobProUserId } from 'src/get-joprouserid.decorator';
import { ApiTags } from '@nestjs/swagger'
import { LogService } from 'src/log/log.service';

@ApiTags('Work Circle')
@Controller('v2/work-circle')
export class WorkcircleController {
    constructor(private workCircleService: WorkCircleService, private logService: LogService) { }
    @Get()
    getWorkCircle(@GetJobProUserId() jobId: string, @GetTenantId() tenantId: string) {
        this.logService.log('JobProId: ', jobId)
        return this.workCircleService.getTopCollaborators(tenantId, jobId)
    }
    @Get('daily-message-count/:jobId')
    getDailyUserSentMessageCount(@Param('jobId') jobId: string, @GetUser() user: UserDto, @Query('startDate') startDate: Date, @Query('endDate') endDate: Date) {
        return this.workCircleService.getDailyUserSentMessageCount(jobId, startDate, endDate)
    }
}
