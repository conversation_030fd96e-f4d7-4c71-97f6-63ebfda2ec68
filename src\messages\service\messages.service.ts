import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { LogService } from 'src/log/log.service';
import { CreateMessageDto } from 'src/messages/dto/create-message.dto';
import { MessageDto } from 'src/messages/dto/message.dto';
import { PinMessageDto } from 'src/messages/dto/pin-message.dto';
import { UserService } from 'src/user/services/v1/user.service';
import { FileService } from '../../file/file.service';
import { GetPrivateMessageByPageDto, GetPrivateMessageDto } from '../dto/get_private_message.dto';
import { RoomDto } from '../dto/room.dto';
import { ReactionDto } from '../dto/reaction.dto';
import { MESSAGE_TYPE } from '../dto/message_type.enum';
import { CHAT_CALL_FORMAT, CHAT_CALL_TYPE } from '../dto/chat-call.dto';
import { MailgunService } from 'src/mail/services/mailgun.service';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class MessagesService {


  constructor(
    @InjectModel('Message') private readonly messageModel: Model<MessageDto>,
    @InjectModel('Room') private readonly roomModel: Model<RoomDto>,
    private logService: LogService,
    private userService: UserService,
    private configService: ConfigService,
    private fileService: FileService,
    private mailgunService: MailgunService
  ) { }
  async createCallMessage(callData: { user: string; messageType: MESSAGE_TYPE; room: string; receiverId: string; is_private: boolean; messageId: string; duration: string, callType: CHAT_CALL_FORMAT }) {
    const found = await this.messageModel.findOne({ messageId: callData.messageId });
    if (found) {
      return await this.messageModel.findOneAndUpdate({ messageId: callData.messageId }, { ...callData, room: callData.room ? new mongoose.Types.ObjectId(callData.room) : null, });
    }
    return await this.create({ ...callData, room: callData.room })

  }

  async removeReaction(reactionId: string, message: string) {
    await this.messageModel.updateOne({ _id: message }, { $pull: { reactions: reactionId } })
  }
  async addReaction(reactionId: string, message: string) {
    try {

      return await this.messageModel.updateOne({ messageId: message }, { $push: { reactions: new mongoose.Types.ObjectId(reactionId) } });
    } catch (error) {
      this.logService.error('adding reaction error: ', error)
    }
  }
  async getDialogs(userId: string) {
    const user =
      (await this.userService.findOne(userId)) ||
      (await this.userService.findByUserId(userId));
    this.logService.log('user for dialogs: ', user);

    // Find messages that match the criteria
    const dialogs = await this.messageModel.find({
      $or: [
        { mentions: user.user_id },
        { mentions: { $ne: [] }, user: new mongoose.Types.ObjectId(userId) }
      ]
    });

    // Populate the user, receiverId, and room fields
    await this.messageModel.populate(dialogs, [
      { path: 'user', select: 'username email companyId tenantId colorPreference _id' },
      { path: 'receiverId', select: 'username' },
      { path: 'room', select: 'name' },
      { path: 'reactions', populate: { path: 'user', model: 'User', select: 'username id' } }
    ]);
    this.logService.log('dialog 1: ', dialogs[0])
    // Filter for messages with replies



    const updatedDialogs = [];
    for (const dialog of dialogs) {
      const replies = await this.messageModel.find({ parentId: dialog.messageId });
      await this.messageModel.populate(replies, [
        { path: 'user', select: 'username email companyId tenantId colorPreference _id' },
        { path: 'receiverId', select: 'username' },
        { path: 'room', select: 'name' },
        { path: 'reactions', populate: { path: 'user', model: 'User', select: 'username id' } }
      ]);
      this.logService.log('replies: ', replies)
      for (const reply of replies) {
        // dg

        reply.files = await Promise.all(reply.files.map(async (file) => ({
          ...file,
          url: await this.fileService.getGCSFilePublicUrl(file.filename),
        })));
      }

      dialog.files = await Promise.all(await dialog.files.map(async (file) => ({
        ...file,
        url: await this.fileService.getGCSFilePublicUrl(file.filename),
      })));

      // Push the updated dialog with replies to the result array

      updatedDialogs.push({ ...dialog.toJSON(), replies });
      // updatedDialogs.push(dialog.toJSON());
      // updatedDialogs.push(...replies)
    }

    // Return the updated dialogs
    this.logService.log('updated dialogs: ', updatedDialogs);
    return updatedDialogs;


  }
  async pinMessage(pinMessage: PinMessageDto) {
    this.logService.log('pin message data: ', pinMessage);
    const up = await this.messageModel.updateOne(
      { messageId: pinMessage.messageId },
      { isPinned: pinMessage.isPinned, pinnedBy: pinMessage.pinnedBy },
    );
    if (up.modifiedCount) {
      const message = pinMessage.isPinned
        ? 'Message is pinned successfully'
        : 'Messaged is unpinned successfully';
      return { message };
    } else {
      const message = pinMessage.isPinned
        ? 'Message is pinning failed'
        : 'Unpinning messaged failed';

      return { message };
    }
  }

  async getChannelMessages(id: string) {
    if (!id) throw new BadRequestException('room id is required to get room messages')
    this.logService.log('id for messages: ', id);
    const roomId = new mongoose.Types.ObjectId(id);
    this.logService.log('room Id for messages: ', roomId);
    const messages = await this.messageModel.find({ room: roomId }).populate([
      {
        path: 'user',
        model: 'User', // populate connections,
        select: '-messages -rooms -connections',
      },
      {
        path: 'reactions',
        model: 'Reaction',
        populate: {
          path: 'user',
          model: 'User',
          select: '-messages -rooms -connections',
        }
      }
    ]);
    this.logService.log('channel messages: ', messages.length);
    // Create an array of promises to retrieve public URLs for all files in all messages

    // Return the updatedMessages array to the user
    return this.getMessagesFileUrl(messages);
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async updateMessageFileUrls() {
    this.logService.log('initating cron file urls update')
    const fileMessages = await this.messageModel.find({ files: { $ne: [] } });
    const updatedFileUrls = await this.getMessagesFileUrl(fileMessages);
    const updates = updatedFileUrls.map(message => {
      this.messageModel.updateOne({ _id: message.id }, { files: message.files }).then(res => {
        // this.logService.log('file url update res:', res)

      })
    });
    // const res = await this.messageModel.bulkWrite(updates);
    // this.logService.log('message files cron update: ', res)
  }
  async getChannelMessageTotalPage(id: string, pageSize: number) {
    const roomId = new mongoose.Types.ObjectId(id);
    const count = await this.messageModel.countDocuments({ room: roomId })
    return { totalPage: Math.ceil(count / pageSize) }
  }
  async getChannelMessagesByPages(id: string, page?: number, pageSize?: number) {
    if (!id) throw new BadRequestException('room id is required to get room messages')
    this.logService.log('id for messages: ', id);
    const roomId = new mongoose.Types.ObjectId(id);
    this.logService.log('room Id for messages: ', roomId);

    const messages = await this.messageModel.find({ room: roomId })
      .sort({ createdAt: -1 })
      .skip((page || 0) * (pageSize || 3))
      .limit(pageSize || 3)
      .populate([
        {
          path: 'user',
          model: 'User', // populate connections,
          select: '-messages -rooms -connections',
        },
        {
          path: 'reactions',
          model: 'Reaction',
          populate: {
            path: 'user',
            model: 'User',
            select: '-messages -rooms -connections',
          }
        }
      ]);
    this.logService.log('channel messages: ', messages.length);


    return messages;//this.getMessagesFileUrl(messages);
  }


  async getMessagesFileUrl(messages: MessageDto[]) {
    const updatedMessages = await Promise.all(
      messages.map(async (message) => {
        if (message.files.length) {
          const files = message.files;

          const updatedFiles = await Promise.all(
            files.map(async (file) => {
              // this.logService.log('file: ', file)
              if (file) {
                const publicUrl = await this.fileService.getGCSFilePublicUrl(
                  file?.filename,
                );
                return { ...file, url: publicUrl };
              }
            }),
          );

          message.files = updatedFiles;
        }

        return message;
      }),
    );
    return updatedMessages;
  }
  /**
   * This needs to be updated with sub domain parameter
   * @param userId 
   * @returns 
   */
  async getAllUnReadMessages(userId: string, subdomain: string) {
    let unReadCount = await this.messageModel.count({
      receiverId: userId,
      readBy: { $ne: userId },
    });
    this.logService.log('unReadCount: ', unReadCount);

    const roomMessages = await this.roomModel.find({ users: userId, subdomain: subdomain }).populate({
      path: 'messages',
      populate: [
        {
          path: 'user',
          model: 'User',
        },
      ],
    });

    // this.logService.log(' room messages: ', roomMessages);
    for (let i = 0; i < roomMessages.length; i++) {
      unReadCount += roomMessages[i].messages.filter(
        (m) => !m.readBy.includes(userId),
      ).length;
    }
    this.logService.log('unReadCount: ', unReadCount);

    return unReadCount;
  }

  async updateReadStatus(data: { userId: string, id?: string, messageId?: string }) {
    this.logService.log(' update read status: ', data);
    try {
      if (data.id) {
        const found = await this.messageModel.findOne({ _id: data.id });
        if (!found) { throw new NotFoundException('Message with the specified id not found') }
        found.readBy = found.readBy?.length ? [...found.readBy, data.userId] : [data.userId];
        await found.save();

        return await found.populate([{ path: 'user', model: "User", select: '-connections -rooms' }, { path: 'room', model: 'Room', select: '-messages' }]);

      } else {
        const savedMessage = await this.findMessageByMessageId(data.messageId);
        if (!savedMessage) { throw new NotFoundException('Message with the specified id does not exist') }
        savedMessage.readBy = savedMessage.readBy?.length ? [...savedMessage.readBy, data.userId] : [data.userId];
        await savedMessage.save();

        return savedMessage;
      }

    } catch (error) {
      this.logService.log('error updating read status: ', error)
    }

  }
  async getPrivateMessages(data: GetPrivateMessageDto) {
    if (!data.connectionId || !data.userId) {
      throw new BadRequestException('connection id and user id are required');
    }
    const userId = new mongoose.Types.ObjectId(data.userId.toString());
    const connectionId = new mongoose.Types.ObjectId(data.connectionId);
    const messages = await this.messageModel
      .find({
        $or: [
          { user: userId, receiverId: connectionId },
          { user: connectionId, receiverId: userId },
        ],
      })
      .populate([{
        path: 'user',
        model: 'User',
        select: '-messages -rooms -connections',
      }
        , {
        path: 'reactions',
        model: 'Reaction',
        populate: {
          path: 'user',
          model: 'User',
          select: '-messages -rooms -connections',
        }
      }]);
    // this.logService.log('private messages: ', messages);

    return messages;//this.getMessagesFileUrl(messages);
  }
  async getPrivateMessageTotalPage(uId: string, cId: string, pageSize: number) {
    const userId = new mongoose.Types.ObjectId(uId);
    const connectionId = new mongoose.Types.ObjectId(cId);
    const count = await this.messageModel.countDocuments({ user: userId, receiverId: connectionId })
    this.logService.log('private count: ', count)
    return { totalPage: Math.ceil(count / pageSize) }
  }
  async getPrivateMessagesByPage(data: GetPrivateMessageByPageDto) {
    if (!data.connectionId || !data.userId) {
      throw new BadRequestException('connection id and user id are required');
    }
    const userId = new mongoose.Types.ObjectId(data.userId.toString());
    const connectionId = new mongoose.Types.ObjectId(data.connectionId);
    const page = data.page || 0;
    const pageSize = data.pageSize || 3;

    const messages = await this.messageModel
      .find({
        $or: [
          { user: userId, receiverId: connectionId },
          { user: connectionId, receiverId: userId },
        ],
      }).sort({ createdAt: -1 })
      .skip(page * pageSize)
      .limit(pageSize)
      .populate([{
        path: 'user',
        model: 'User',
        select: '-messages -rooms -connections',
      }
        , {
        path: 'reactions',
        model: 'Reaction',
        populate: {
          path: 'user',
          model: 'User',
          select: '-messages -rooms -connections',
        }
      }]);
    // this.logService.log('private messages: ', messages);

    return messages;//this.getMessagesFileUrl(messages);
  }
  async markMessageDeleted(message: MessageDto) {
    await this.messageModel.updateOne({ _id: message.id }, { isDeleted: true });
    const found = await this.messageModel.findOne({ _id: message.id });

    return found;
  }
  async findMessageByMessageId(messageId: string) {
    return await this.messageModel.findOne({ messageId })
  }
  async create(message: CreateMessageDto): Promise<any> {
    try {
      this.logService.log('message to save: ', message);
      // message.user = new mongoose.Types.ObjectId(message.user);
      // let parentId = null;
      if (message?.text?.length > 0 || message?.files?.length > 0 || message.messageType == MESSAGE_TYPE.CALL) {
        // if (message.parentId) {
        //   if (message.parentId) {
        //     const parentMessage = await this.findMessageByMessageId(message.parentId);
        //     parentId = parentMessage.id
        //   }
        // }
        const newMessage = new this.messageModel({ ...message, user: new mongoose.Types.ObjectId(message.user.toString()), room: message.room ? new mongoose.Types.ObjectId(message.room.toString()) : null });
        const saved = await newMessage.save();
        if (!message.is_private) {
          await this.addMessageToRoomAndUser(
            message.user['id'],
            newMessage._id.toString(),
            message.room['id'],
          );
        }
        const populatedMessage = await this.messageModel.findOne({ _id: saved.id })
          .populate({
            path: 'user',
            model: 'User',
            select: '-connections -rooms'
          }).populate({ path: 'room', model: 'Room', select: '-users -messages' })
          .populate({
            path: 'receiverId',
            model: 'User',
            select: '-connections -rooms'
          });
        // await populatedMessage.populate({ path: 'room', model: 'Room', select: '-users -messages' })
        this.logService.log('populatedMessage: ', populatedMessage);
        if (message.mentions.length) {
          const emails = [];
          for (let i = 0; i < message.mentions.length; i++) {
            this.logService.log('user id: ', message.mentions[i])
            const user = await this.userService.findByUserIdWithoutMsg(message.mentions[i]);
            this.logService.log('user found: ', user)
            emails.push(user?.email?.toLowerCase());
          }

          // const room = await this.roomModel.findById(message.room);
          const roomName = populatedMessage.room['name']
          this.logService.log('room..: ', roomName, ' subdomain: ', message.subdomain)
          const html = this.mailgunService.mentionHTML(message.subdomain, roomName, '');
          this.mailgunService.sendMail({ to: emails, html, subject: 'New Mention in JobChat' })
        }
        return populatedMessage;
      } else
        throw new BadRequestException(
          'a valid message must have at least either text or file content',
        );
    } catch (error) {
      this.logService.log('error saving message: ', error.message);
    }
  }

  async findMessagesForRoom(roomId: string): Promise<MessageDto[]> {
    const messages = await this.messageModel
      .find({ room: new mongoose.Types.ObjectId(roomId) })
      .populate('room, user');

    return await messages;//this.getMessagesFileUrl(messages);
  }

  private async addMessageToRoomAndUser(
    userId: string,
    messageId: string,
    roomId: string | string,
  ) {
    // await this.userModel.findByIdAndUpdate(
    //   userId,

    //   { $push: { messages: messageId } },
    //   { new: true, useFindAndModify: false },
    // );
    await this.roomModel.findByIdAndUpdate(
      roomId,
      { $push: { messages: messageId } },
      { new: true, useFindAndModify: false },
    );
    return;
  }

}
