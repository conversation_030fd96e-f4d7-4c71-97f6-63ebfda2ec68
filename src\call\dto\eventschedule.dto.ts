import { ApiProperty, } from "@nestjs/swagger";
import { IsDate, IsString } from "class-validator";

export class ScheduleEventDto {
    @ApiProperty()
    @IsDate()
    date: Date;

    @ApiProperty()
    @IsString()
    startTime: string;

    @ApiProperty()
    @IsString()
    endTime: string;

    @ApiProperty()
    @IsString()
    userId: string;// id of the person creating the schedule

    @ApiProperty()
    @IsString()
    participantId: string;//user id of the other user the schedule is for

    @ApiProperty()
    @IsString()
    status: string;

    @ApiProperty()
    @IsString()
    eventId?: string;

    @ApiProperty()
    @IsString()
    senderName: string;

    @ApiProperty()
    @IsString()
    topic: string;

    @ApiProperty()
    @IsString()
    timeZone: string;

    @ApiProperty()
    @IsString()
    message: string;


    // @IsOptional()
    // _id: string;
}