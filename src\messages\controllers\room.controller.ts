/* eslint-disable prettier/prettier */
/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Delete, Get, Param, Patch, Post } from '@nestjs/common';
import { UpdateRoomDto } from '../dto/room.dto';
import { RoomService } from '../service/room.service';
import { LogService } from 'src/log/log.service';

@Controller('v1/room')
export class RoomController {
    constructor(private roomService: RoomService, private logService: LogService) { }
    /**
     * 
     * @param data 
     * @returns 
     */
    @Post('')
    createRoom(@Body() data: any) {
        return this.roomService.createRoom(data.room, data.user);
    }
    /**
     * 
     * @param channelId 
     * @returns 
     */
    @Get('get-channel-members/:channelId')
    getChannelMembers(@Param('channelId') channelId: string) {

        return this.roomService.getRoomMembers(channelId)
    }
    /**
     * 
     * @param room 
     * @param channelId 
     * @returns 
     */
    @Patch('update-channel/:channelId')
    updateChannel(@Body() room: UpdateRoomDto, @Param('channelId') channelId: string) {
        return this.roomService.updateChannel(channelId, room);
    }

    /**
     * 
     * @param channelId 
     * @returns 
     */
    @Get('get-channel-files/:channelId')
    getRoomFiles(@Param('channelId') channelId: string) {
        return this.roomService.getRoomFiles(channelId);
    }
    /**
     * 
     * @param userId 
     * @param roomId 
     * first parameter is id of user to remove from a channel
     * second parameter is the id of the channel to remove the user from
     * @returns 
     */
    @Delete('remove-user-from-channel/:userId/:roomId')
    removeUserFromRoom(@Param('userId') userId: string, @Param('roomId') roomId: string) {
        return this.roomService.leaveRoom(roomId, userId);
    }
    /**
     * 
     * @param channelId 
     * @param userId 
     * @returns 
     */
    @Post('archive/:channelId/:userId')
    archiveChannel(@Param('channelId') channelId: string, @Param('userId') userId: string) {
        return this.roomService.archieveChannel(channelId, userId);
    }

    @Post('unarchive/:channelId/:userId')
    unArchiveChannel(@Param('channelId') channelId: string, @Param('userId') userId: string) {
        return this.roomService.unArchieveChannel(channelId, userId);
    }
    @Get('get-archived-channels/:userId')
    getArchivedChannels(@Param('userId') userId: string) {
        return this.roomService.getArchivedChannels(userId);
    }
}
