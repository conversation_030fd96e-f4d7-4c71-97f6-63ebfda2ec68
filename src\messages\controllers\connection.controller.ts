/*
https://docs.nestjs.com/controllers#controllers
*/

import { Controller, Get, Param, Post } from '@nestjs/common';
import { ConnectionService } from '../service/connection.service';

@Controller('v1/connection')
export class ConnectionController {
    constructor(private connectionService: ConnectionService) {

    }
    @Post('archive/:connectionId')
    archiveConnection(@Param('connectionId') connectionId: string) {
        return this.connectionService.archiveConnection(connectionId);
    }
    @Post('unarchive/:connectionId')
    unArchiveConnection(@Param('connectionId') connectionId: string) {
        return this.connectionService.unArchiveConnection(connectionId);
    }
    @Get('get-archived-connections/:userId')
    getArchivedConnections(@Param('userId') userId: string) {
        return this.connectionService.getArchivedConnections(userId)
    }
    @Get('pending-requests/:id')
    findPendingRequest(@Param('id') id: string) {
        return this.connectionService.getActiveConnections(id);
    }

}
