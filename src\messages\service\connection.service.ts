/* eslint-disable prettier/prettier */
/*
https://docs.nestjs.com/providers#services
*/

import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConnectionDto } from 'src/messages/dto/connection.dto';

@Injectable()
export class ConnectionService {


    constructor(
        @InjectModel('Connection')
        private readonly connectionModel: Model<ConnectionDto>,
    ) { }
    getPendingRequests(id: string) {
        throw new Error('Method not implemented.');
    }
    async getActiveConnections(userId: string): Promise<ConnectionDto[] | PromiseLike<ConnectionDto[]>> {
        return await this.connectionModel.find({ $or: [{ userId: userId }, { sender: userId }], isArchived: false })
    }
    async deleteRequest(sender: string, userId: string) {
        await this.connectionModel.deleteOne({ sender, userId });
    }
    async createRequest(data: ConnectionDto) {
        return await this.connectionModel.create(data);
    }
    async archiveConnection(connectionId: string) {
        if (!connectionId) throw new BadRequestException('Connection id is required to archive a connection');
        const found = (await this.connectionModel.findById(connectionId)).toJSON();
        if (!found) throw new NotFoundException('Connection with the specified id does not exist');

        await this.connectionModel.updateOne({ _id: found.id }, { isArchived: true });
        return { message: 'The connection has been successfully archived' };
    }
    async getArchivedConnections(userId: string) {
        if (!userId) throw new BadRequestException('Connection id is required to archive a connection');

        return await this.connectionModel.find({ $or: [{ userId: userId }, { sender: userId }], isArchived: true })
    }
    async unArchiveConnection(connectionId: string) {
        if (!connectionId) throw new BadRequestException('Connection id is required to archive a connection');
        const found = (await this.connectionModel.findById(connectionId)).toJSON();
        if (!found) throw new NotFoundException('Connection with the specified id does not exist');

        await this.connectionModel.updateOne({ _id: found.id }, { isArchived: false });
        return { message: 'The connection has been successfully un-archived' };
    }
}
