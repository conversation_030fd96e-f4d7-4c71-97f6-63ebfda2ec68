import { ApiProperty, PartialType } from "@nestjs/swagger";
import { IsArray, IsBoolean, IsDateString, IsEnum, IsNumber, IsObject, IsOptional, IsString } from "class-validator";
import { Types } from "mongoose";
import { Socket } from "socket.io";
import { FileDto } from "src/messages/dto/file.dto";
import { MESSAGE_TYPE } from "src/messages/dto/message_type.enum";
import { ChatUser } from "src/models/chat-user.model";
import { CHAT_TYPE, EncryptedKey } from "src/models/chat.model";


export class ChatDto {

    @IsString()
    name: string;

    @IsString() createdBy: string;

    @IsString()
    @IsOptional() symmetricKey?: string;
    @IsString()
    @IsOptional()
    encryptedKeys?: Array<EncryptedKey>;

    @IsString()
    @IsOptional() description?: string;

    @IsBoolean()
    @IsOptional() isArchived?: boolean;

    @IsArray() users: string[];

    @IsArray() jobProUserIds: string[];

    @IsString() tenantId: string;

    @IsDateString()
    @IsOptional() createdAt?: Date;

    @IsDateString()
    @IsOptional() updatedAt?: Date;

    @IsBoolean()
    @IsOptional() isPrivate?: boolean;

    @IsString()
    @IsOptional() archivedBy?: string;

    @IsEnum(CHAT_TYPE) chatType: CHAT_TYPE;

    @IsString()
    @IsOptional() id?: string;
    @IsBoolean()
    @IsOptional() isGeneral: boolean;
    @IsBoolean()
    @IsOptional() isPinned?: boolean;
    @IsString()
    @IsOptional() pinnedBy?: string;

    @IsOptional()
    @IsArray() emails?: string[];
    @IsOptional()
    @IsNumber() lastMessageTimestamp?: number;

}

export class ChatUserDto {
    @ApiProperty()
    @IsString() jobProUserId: string;

    @ApiProperty()
    @IsString() username: string;

    @ApiProperty()
    @IsString() tenantId: string[];

    @ApiProperty()
    @IsString()

    @ApiProperty()
    @IsOptional() chats: string[];

    @ApiProperty()
    @IsString()
    @IsOptional() colorPreference?: string;

    @IsString()
    @IsOptional() id?: string;

    @IsString()
    @IsOptional()
    subdomain?: string;
    @IsString()
    @IsOptional()
    status: string;
    @IsString()
    @IsOptional()
    email?: string;

    @IsString()
    @IsOptional()
    publicKey: string;
    @IsString()
    @IsOptional()
    privateKey: string;

    @IsString()
    @IsOptional()
    activeChatId: string;

    @IsObject()
    @IsOptional()
    sockets?: Socket[];
}

export class ChatMessageDto {
    @ApiProperty()
    @IsString()
    @IsOptional() text?: string;

    @ApiProperty()
    @IsString()
    @IsOptional() fID?: string;

    @ApiProperty()
    @IsArray()
    @IsOptional() files?: FileDto[];

    @ApiProperty()
    @IsString() chatId: string;
    @IsString() senderJobProUserId?: string;

    @IsString() sender?: string;

    @ApiProperty()
    @IsArray()
    @IsOptional()
    mentions?: string[];

    @ApiProperty()
    @IsArray()
    @IsOptional() reactions?: [{ userId: string, reaction: string }];

    @ApiProperty()
    @IsOptional()
    @IsBoolean() isPinned?: boolean;

    @ApiProperty()
    @IsOptional()
    @IsString() pinnedBy?: string;

    @IsOptional()
    @IsDateString() createdAt?: Date;
    @IsOptional()
    @IsDateString() updatedAt?: Date;

    @ApiProperty()
    @IsBoolean()
    @IsOptional()
    isFirstChatMessage?: boolean;

    @ApiProperty()
    @IsOptional()
    @IsBoolean() isDeleted?: boolean;

    @ApiProperty()
    @IsEnum(MESSAGE_TYPE) messageType?: MESSAGE_TYPE;

    @ApiProperty()
    @IsOptional()
    @IsNumber() callDuration?: number;

    @ApiProperty()
    @IsOptional()
    @IsString() callType?: string;

    @ApiProperty()
    @IsOptional()
    @IsArray() readBy?: string[];

    @ApiProperty()
    @IsOptional()
    @IsString() parentId?: string;

    @ApiProperty()
    @IsOptional()
    @IsString()
    callStatus?: string;

    @ApiProperty()
    @IsString()
    @IsOptional() callId?: string;

    @ApiProperty()
    @IsString()
    @IsOptional()
    subdomain?: string;
    @IsString()
    @IsOptional()
    addedJobProUserIds?: string[];

    @IsBoolean()
    @IsOptional()
    e2e?: boolean;
    @IsString()
    @IsOptional() id?: string;
    @IsOptional()
    @IsBoolean() isSystemGenerated?: boolean;
    @IsOptional()
    @IsBoolean() isEdited?: boolean;
    @IsOptional()
    @IsBoolean() isForwarded?: boolean;
    _id?: string | Types.ObjectId;

}
export class ChatMessageHttpDto extends PartialType(ChatMessageDto) {
    @IsString()
    @IsOptional() email?: string;
    @IsOptional()
    @IsString() jobId: string;
}
export class CreateChatUserDto {
    @ApiProperty()
    @IsArray() tenantId: string;

    @ApiProperty()
    @IsString() jobProUserId: string;

    @ApiProperty()
    @IsString() email: string;
}

export class JobleServiceUserEvent {
    @ApiProperty()
    @IsString() TenantId: string;
    @ApiProperty()
    @IsString() Id: string;
    @ApiProperty()
    @IsString() Email: string;
}


export class ChatActivityDto {
    @ApiProperty() sender: ChatUserDto | string;
    @ApiProperty() user: ChatUserDto | string;
    @ApiProperty() tenantId: string;
    @ApiProperty() activityType: ACTIVITY_TYPE;
    @ApiProperty() chat: ChatDto | string;
    @ApiProperty() inviteType?: INVITE_TYPE;
    @ApiProperty() isRead?: boolean;
    @ApiProperty() message?: ChatMessageDto | string;// remember to include the chat here;
    @ApiProperty() id?: string;
    @ApiProperty() createdAt?: Date;
    @ApiProperty() updatedAt?: Date;

}


export class DraftDto {
    @ApiProperty()
    @IsString()
    chatId: string;
    @IsString()
    @ApiProperty() jobProUserId: string;
    @IsOptional()
    @IsArray() @ApiProperty()
    files?: FileDto[];
    @IsOptional()
    @IsString() @ApiProperty()
    text?: string;
}
export class MessagePaginationDto {
    @ApiProperty()
    @IsOptional()
    @IsString()
    page: number;

    @ApiProperty()
    @IsString()
    @IsOptional()
    pageSize: number;
}

export enum ACTIVITY_TYPE {
    REACTION = 'REACTION',
    MENTION = 'MENTION',
    INVITE = 'INVITE',

}
export enum INVITE_TYPE {
    REMOVED = 'REMOVED',
    ADDED = "ADDED",
    LEFT = "LEFT",
    JOINED = 'JOINED',
}
export class UpdateChatUserDto extends PartialType(ChatUserDto) { }
export class UpdateChatDto extends PartialType(ChatDto) { }
export class UpdateChatMessageDto extends PartialType(ChatMessageDto) { };