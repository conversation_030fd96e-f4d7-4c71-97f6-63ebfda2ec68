/**
 * This model keeps record of all online users
 */

import { <PERSON><PERSON>, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';

@Schema({
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret.__v;
      ret.id = ret._id;
      delete ret._id;
    },
  },
  toObject: { virtuals: true },
},)
export class ConnectedUser {
  @Prop({ required: true, unique: true }) socketId: string;
  @Prop({ type: Types.ObjectId, ref: 'User', required: true }) user: string;
}
export const ConnectedUserSchema = SchemaFactory.createForClass(ConnectedUser)
