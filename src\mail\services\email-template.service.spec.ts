import { Test, TestingModule } from '@nestjs/testing';
import { EmailTemplateService, EmailTemplateType } from './email-template.service';
import { LogService } from 'src/log/log.service';
import * as fs from 'fs';
import * as path from 'path';

describe('EmailTemplateService', () => {
  let service: EmailTemplateService;
  let logService: LogService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailTemplateService,
        {
          provide: LogService,
          useValue: {
            error: jest.fn(),
            log: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<EmailTemplateService>(EmailTemplateService);
    logService = module.get<LogService>(LogService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getMentionTemplate', () => {
    it('should replace variables in mention template', async () => {
      const mentionerName = '<PERSON>';
      const userName = '<PERSON>';
      const circleName = 'Project Alpha';
      const messageText = '@jane can you review this?';
      const actionUrl = 'https://example.com/chat/123';

      // Mock fs.existsSync and fs.readFileSync
      const mockTemplate = `
        <html>
          <body>
            <p>Hi [User Name],</p>
            <p>Someone just mentioned you in the [Circle Name] Circle!</p>
            <p>Who Mentioned You: [Name]</p>
            <p>Message: "@UserName can you help with the new project outline by Friday?"</p>
            <a href="#">Reply to the mention now!</a>
          </body>
        </html>
      `;

      jest.spyOn(fs, 'existsSync').mockReturnValue(true);
      jest.spyOn(fs, 'readFileSync').mockReturnValue(mockTemplate);

      const result = await service.getMentionTemplate(
        mentionerName,
        userName,
        circleName,
        messageText,
        actionUrl
      );

      expect(result).toContain('Hi Jane Smith,');
      expect(result).toContain('Project Alpha Circle!');
      expect(result).toContain('Who Mentioned You: John Doe');
      expect(result).toContain('"@jane can you review this?"');
      expect(result).toContain(`href="${actionUrl}"`);
    });
  });

  describe('getAddedToCircleTemplate', () => {
    it('should replace variables in added to circle template', async () => {
      const userName = 'Jane Smith';
      const circleName = 'Project Alpha';
      const inviterName = 'John Doe';
      const actionUrl = 'https://example.com/chat/123';

      // Mock fs.existsSync and fs.readFileSync
      const mockTemplate = `
        <html>
          <body>
            <p>Hi [User Name],</p>
            <p>You've been added to the [Circle Name] Circle!</p>
            <p>collaborate on all things [Topic Name].</p>
            <a href="#">Join the conversation!</a>
          </body>
        </html>
      `;

      jest.spyOn(fs, 'existsSync').mockReturnValue(true);
      jest.spyOn(fs, 'readFileSync').mockReturnValue(mockTemplate);

      const result = await service.getAddedToCircleTemplate(
        userName,
        circleName,
        inviterName,
        actionUrl
      );

      expect(result).toContain('Hi Jane Smith,');
      expect(result).toContain('Project Alpha Circle!');
      expect(result).toContain('collaborate on all things Project Alpha.');
      expect(result).toContain(`href="${actionUrl}"`);
    });
  });

  describe('error handling', () => {
    it('should throw error when template file does not exist', async () => {
      jest.spyOn(fs, 'existsSync').mockReturnValue(false);

      await expect(
        service.getTemplate(EmailTemplateType.CHAT_MENTION, {})
      ).rejects.toThrow('Template file not found');
    });

    it('should log error when template loading fails', async () => {
      jest.spyOn(fs, 'existsSync').mockReturnValue(true);
      jest.spyOn(fs, 'readFileSync').mockImplementation(() => {
        throw new Error('File read error');
      });

      await expect(
        service.getTemplate(EmailTemplateType.CHAT_MENTION, {})
      ).rejects.toThrow('File read error');

      expect(logService.error).toHaveBeenCalledWith(
        'Error loading email template: ',
        expect.any(Error)
      );
    });
  });
});
