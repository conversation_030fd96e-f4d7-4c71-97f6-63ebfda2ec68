const { MongoClient } = require('mongodb');

async function testDatabaseConnection() {
    try {
        // Using the actual MongoDB connection string from the config
        const uri = 'mongodb://localhost:27017/chatserviceDB';
        const client = new MongoClient(uri);
        
        await client.connect();
        console.log('✅ Connected to MongoDB Atlas');
        
        const db = client.db('chatserviceDB');
        
        // Check what collections exist
        const collections = await db.listCollections().toArray();
        console.log('📁 Available collections:');
        collections.forEach(col => console.log(`  - ${col.name}`));
        
        // Try different possible collection names
        const possibleCollections = ['chatusers', 'chatUsers', 'chat_users', 'users'];
        let userFound = false;
        
        for (const collectionName of possibleCollections) {
            try {
                const collection = db.collection(collectionName);
                const count = await collection.countDocuments();
                console.log(`📊 Collection '${collectionName}' has ${count} documents`);
                
                if (count > 0) {
                    // Find the user that was just created
                    let user = await collection.findOne({ jobProUserId: 'jobpro-789' });
                    
                    if (user) {
                        console.log(`✅ User found in collection '${collectionName}':`);
                        console.log('User ID:', user._id);
                        console.log('JobPro User ID:', user.jobProUserId);
                        console.log('Email:', user.email);
                        console.log('Tenant ID:', user.tenantId);
                        console.log('Created At:', user.createdAt);
                        console.log('Updated At:', user.updatedAt);
                        userFound = true;
                        
                        // Also check for other test users
                        const testUsers = await collection.find({ jobProUserId: { $regex: /^jobpro-/ } }).toArray();
                        console.log(`\n📋 All test users in '${collectionName}':`)
                        testUsers.forEach((testUser, index) => {
                            console.log(`  ${index + 1}. JobPro ID: ${testUser.jobProUserId}, Email: ${testUser.email}, Created: ${testUser.createdAt}`);
                        });
                        break;
                    }
                }
            } catch (error) {
                // Collection doesn't exist, continue to next one
            }
        }
        
        if (!userFound) {
            console.log('❌ User not found in any collection');
            
            // Show a sample of users if any exist
            for (const collectionName of possibleCollections) {
                try {
                    const collection = db.collection(collectionName);
                    const sampleUsers = await collection.find({}).limit(3).toArray();
                    if (sampleUsers.length > 0) {
                        console.log(`📋 Sample users from '${collectionName}':`)
                        sampleUsers.forEach(user => {
                            console.log(`  - JobPro ID: ${user.jobProUserId}, Email: ${user.email}`);
                        });
                    }
                } catch (error) {
                    // Collection doesn't exist, continue to next one
                }
            }
        }
        
        await client.close();
    } catch (error) {
        console.error('❌ Error connecting to database:', error);
    }
}

testDatabaseConnection();
